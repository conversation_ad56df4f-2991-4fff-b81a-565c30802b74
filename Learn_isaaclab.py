# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
四旋翼飞行器强化学习环境

本模块实现了一个基于 CrazyFlie 四旋翼的强化学习环境，用于训练四旋翼飞行器的飞行控制策略。
该环境使用直接强化学习方式 (DirectRLEnv)，智能体需要学习如何控制四旋翼到达随机生成的目标位置。
"""

from __future__ import annotations

import gymnasium as gym
import torch
import math

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg,mdp,ViewerCfg
from isaaclab.envs.ui import BaseEnvWindow
from isaaclab.markers import VisualizationMarkers
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.math import subtract_frame_transforms,random_yaw_orientation
from isaaclab.managers import EventTermCfg as EventTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.utils.assets import NVIDIA_NUCLEUS_DIR
from isaaclab.sensors import ContactSensor,ContactSensorCfg,RayCaster,RayCasterCfg,patterns,LidarSensorCfg
from isaaclab.sensors.ray_caster.patterns import LivoxPatternCfg

##
# 预定义配置
##
from isaaclab_assets import CRAZYFLIE_CFG  # isort: skip
from isaaclab.markers import CUBOID_MARKER_CFG  # isort: skip

from .experimental_terrains import QUADCOPTER_ROUGH_TERRAINS_CFG_FACTORY

class CrazyFlieEnvWindow(BaseEnvWindow):
    """四旋翼环境的窗口管理器，用于创建和管理环境的用户界面。"""

    def __init__(self, env: CrazyFlieEnv, window_name: str = "IsaacLab"):
        """
        初始化窗口。

        Args:
            env: 环境对象。
            window_name: 窗口名称，默认为 "IsaacLab"。
        """
        # 初始化基础窗口
        super().__init__(env, window_name)
        # 添加自定义UI元素
        with self.ui_window_elements["main_vstack"]:
            with self.ui_window_elements["debug_frame"]:
                with self.ui_window_elements["debug_vstack"]:
                    # 添加命令管理器可视化（用于显示目标位置等调试信息）
                    self._create_debug_vis_ui_element("targets", self.env)

@configclass
class EventCfg:
    """
    随机化事件配置
    
    定义训练过程中的域随机化策略，用于提高策略的泛化能力。
    """
    
    # 随机化机体质量
    add_body_mass = EventTerm(
        func=mdp.randomize_rigid_body_mass,  # 使用的随机化函数
        mode="startup",  # 在环境启动时应用
        params={
            # "asset_cfg": SceneEntityCfg("robot", body_names="body"),  # 作用于机器人的机体
            ### 
            "asset_cfg": SceneEntityCfg("robot", body_names="base_link"),  # 作用于机器人的机体
            "mass_distribution_params": (0.975, 1.025),  # 质量随机化范围 [原质量*0.975, 原质量*1.025]
            "operation": "scale",  # 缩放操作
        },
    )


@configclass
class CrazyFlieEnvCfg(DirectRLEnvCfg):
    """      # 姿态惩罚：鼓励保持水平
    定义了四旋翼强化学习环境的所有参数，包括：
    - 环境基本参数（时间步长、动作/观察空间维度等）
    - 物理仿真参数
    - 传感器配置
    - 奖励函数参数
    - 地形和场景配置
    """
    
    # 环境基本设置
    episode_length_s = 15  # 每个回合的时长（秒）
    dt = 1/50   # 物理仿真时间步长（秒），50Hz
    decimation = 2  # 动作重复次数：每个动作会被执行2个仿真步
    action_space = 4  # 动作空间维度：[总推力, 滚转力矩, 俯仰力矩, 偏航力矩]
    observation_space = 59  # 观察空间维度：[线速度(3) + 角速度(3) + 重力向量(3) + 目标位置(3)]
    state_space = 0  # 状态空间维度（这里不使用特权信息）
    debug_vis = False  # 始终启用调试可视化


    # === 地形参数 ===
    size_terrain = 25.0  # 增大地形大小
    objects_density_min = 0.17  # 极低的障碍物最小密度
    objects_density_max = 0.7  # 极低的障碍物最大密度

    # === 训练优化参数 ===
    random_respawn = False  # 是否随机重生到不同地形
    avoid_reset_spikes_in_training = True  # 避免训练时重置峰值，通过随机化episode长度实现

    # 指定UI窗口类型
    ui_window_class_type = CrazyFlieEnvWindow

    # === 物理仿真配置 ===
    sim: SimulationCfg = SimulationCfg(
        dt=dt,  # 仿真时间步长：20ms
        render_interval=decimation,  # 渲染间隔
        # disable_contact_processing=True,  # 禁用接触处理以提高性能
        # 物理材质配置（用于碰撞检测）
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",  # 摩擦力组合模式
            restitution_combine_mode="multiply",  # 恢复系数组合模式
            static_friction=1.0,  # 静摩擦系数
            dynamic_friction=1.0,  # 动摩擦系数
            restitution=0.0,  # 恢复系数（弹性碰撞系数）
        ),
    )
    
    # 地形配置
    terrain = TerrainImporterCfg(
        prim_path="/World/ground",
        terrain_type="generator",  # 使用生成地形
        # 地面的物理材质配置
        terrain_generator=QUADCOPTER_ROUGH_TERRAINS_CFG_FACTORY(
            size=size_terrain,  # 地形大小
            density_min=objects_density_min,  # 最小障碍物密度
            density_max=objects_density_max,   # 最大障碍物密度
            num_cols=5,
            num_rows=5
        ),
        max_init_terrain_level=None,  # 最大初始地形难度级别
        collision_group=-1,  # 碰撞组
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
        visual_material=sim_utils.MdlFileCfg(
            mdl_path=f"{NVIDIA_NUCLEUS_DIR}/Materials/Base/Architecture/Shingles_01.mdl",  # 地形材质
            project_uvw=True,  # 启用UV投影
        ),
        debug_vis=False,  # 不显示地形调试信息
    )

    # # 地形配置
    # terrain = TerrainImporterCfg(
    #     prim_path="/World/ground",
    #     terrain_type="plane",  # 使用平面地形
    #     collision_group=-1,  # 碰撞组
    #     # 地面的物理材质配置
    #     physics_material=sim_utils.RigidBodyMaterialCfg(
    #         friction_combine_mode="multiply",
    #         restitution_combine_mode="multiply",
    #         static_friction=1.0,
    #         dynamic_friction=1.0,
    #         restitution=0.0,
    #     ),
    #     debug_vis=False,  # 不显示地形调试信息
    # )
    # === 视角配置 ===
    viewer = ViewerCfg(
        eye=(25.0, 25.0, 20.0),  # 相机位置设置更近
        lookat=(6.5, 6.5, 0),  # 相机焦点对准飞机高度
        resolution=(1920, 1080)   # 渲染分辨率
    )

    # 场景配置
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=2048,  # 只使用一个环境
        env_spacing=2.5,  # 环境间距（不再重要，因为只有一个环境）
        replicate_physics=True  # 复制物理属性到所有环境
    )

    events: EventCfg = EventCfg()
    # 机器人配置
    robot: ArticulationCfg = CRAZYFLIE_CFG.replace(prim_path="/World/envs/env_.*/Robot")
            
    
    # # === 激光雷达传感器配置 ===
    # simple_lidar = RayCasterCfg(
    #     # prim_path="/World/envs/env_.*/Robot/body",  # 传感器挂载位置
    #     prim_path="/World/envs/env_.*/Robot/base_link",  # 传感器挂载位置
    #     update_period=dt * decimation,  # 更新周期
    #     offset=RayCasterCfg.OffsetCfg(pos=(0.0, 0.0, 0.07)),  # 相对机体的偏移
    #     mesh_prim_paths=["/World/ground"],  # 检测的网格路径
    #     max_distance=15,  # 最大检测距离（米）
    #     attach_yaw_only=False,  # 是否只绑定偏航角
    #     pattern_cfg=patterns.LidarPatternCfg(
    #         channels=1,  # 激光雷达通道数
    #         vertical_fov_range=(-0.0, 59.0),  # 垂直视场角范围（度）
    #         horizontal_fov_range=(-90.0, 90.0),  # 水平视场角范围（度）
    #         horizontal_res=3.99,  # 水平分辨率（度），约46个射线
    #     ),
    #     debug_vis=True,  # 调试可视化

    # )
    # Realistic Livox sensor with .npy patterns
    simple_lidar = LidarSensorCfg(
        prim_path="/World/envs/env_.*/Robot/base_link",  # 传感器挂载位置
        pattern_cfg=LivoxPatternCfg(
            sensor_type="mid360",
            use_simple_grid=False,  # Use realistic patterns
            samples=8000,
            downsample=1,
        ),
        max_distance=50.0,
        min_range=0.1,
        return_pointcloud=True,
        enable_sensor_noise=True,
        random_distance_noise=0.02,
        mesh_prim_paths=["/World/ground"],
    )
    scaling_lidar_data_b = 1/6.0  # 激光雷达数据缩放因子
    
    # === 接触传感器配置 ===
    contact_sensor: ContactSensorCfg = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Robot/.*",  # 监测所有机器人部件的接触
        history_length=2,  # 历史数据长度
        update_period=dt,   # 更新周期
        track_air_time=False,  # 是否追踪空中时间
        debug_vis=False,
        
    )

    # 四旋翼物理参数
    # === 控制参数 ===
    thrust_to_weight = 1.9  # 推重比：最大推力与重力的比值（>1表示能够上升）
    moment_scale = 0.01     # 力矩缩放因子：控制四旋翼的姿态控制灵敏度
    thrust_scale = 0.75     # 力矩缩放因子

    # === 任务参数 ===
    # 目标位置参数
    desired_pos_w_height_limits = (2.0, 3.0)  # 目标位置高度范围（米）
    desired_pos_b_xy_limits = (size_terrain/2-1.0, size_terrain/2)  # 目标位置xy范围
    desired_pos_b_obs_clip = 4.0  # 观察中目标位置的裁剪范围

    # 飞行高度限制
    height_w_limits = (0.5, 4.5)  # 硬性高度限制（米）
    
    # 速度和角度阈值
    lin_vel_max_soft_thresh = 0  # 线速度软阈值
    ang_vel_final_dist_goal_thresh = 0.3  # 接近目标时的角速度阈值
    
    # 奖励函数标准差参数
    progress_to_goal_std = math.sqrt(0.1)  # 目标进度标准差
    distance_to_goal_std = math.sqrt(1.25)  # 目标距离标准差
    distance_to_goal_fine_std = math.sqrt(0.3)  # 精细目标距离标准差

    # 安全阈值
    threshold_obstacle_proximity = 0.4  # 障碍物接近阈值（米）
    threshold_height_bounds_proximity = 0.3  # 高度边界接近阈值（米）
    height_w_soft_limits = (
        height_w_limits[0] + threshold_height_bounds_proximity,
        height_w_limits[1] - threshold_height_bounds_proximity
    )  # 软性高度限制

    # === 奖励权重参数 ===
    lin_vel_reward_scale = -0.018          # 线速度惩罚权重
    ang_vel_reward_scale = -0.06           # 角速度惩罚权重  
    ang_vel_final_reward_scale = -0.2      # 接近目标时角速度惩罚权重
    actions_reward_scale = -0.2            # 动作幅度惩罚权重
    progress_to_goal_reward_scale = 2.0    # 目标进度奖励权重
    distance_to_goal_reward_scale = 1.0    # 目标距离奖励权重
    distance_to_goal_fine_reward_scale = 0.7  # 精细目标距离奖励权重
    undesired_contacts_reward_scale = -4.0 # 不期望接触惩罚权重
    flat_orientation_reward_scale = -1.0   # 姿态偏斜惩罚权重
    obstacle_proximity_reward_scale = -6.0 # 障碍物接近惩罚权重
    height_bounds_proximity_reward_scale = -4.0  # 高度边界接近惩罚权重
    terminated_reward_scale = -200.0       # 终止状态惩罚权重

class CrazyFlieEnv(DirectRLEnv):
    """
    四旋翼飞行器强化学习环境。
    
    该环境训练四旋翼学习飞行到随机生成的目标位置。四旋翼通过控制总推力和三个轴的力矩来实现飞行控制。
    环境会根据四旋翼与目标的距离、速度稳定性等因素给出奖励。
    """
    cfg: CrazyFlieEnvCfg

    def __init__(self, cfg: CrazyFlieEnvCfg, render_mode: str | None = None, **kwargs):
        """
        初始化四旋翼环境。

        Args:
            cfg: 环境配置
            render_mode: 渲染模式
            **kwargs: 额外参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # === 控制相关变量 ===
        # 动作相关的张量缓存
        # 智能体输出的原始动作（归一化到 [-1, 1] 范围）
        self._actions = torch.zeros(self.num_envs, gym.spaces.flatdim(self.single_action_space), device=self.device)
        # 施加到四旋翼基座的总推力 [fx, fy, fz]，这里只在z轴方向有推力
        self._thrust = torch.zeros(self.num_envs, 1, 3, device=self.device)
        # 施加到四旋翼基座的力矩 [mx, my, mz]，用于控制滚转、俯仰和偏航
        self._moment = torch.zeros(self.num_envs, 1, 3, device=self.device)
        
        # === 任务相关变量 ===
        # 目标位置（世界坐标系）
        self._desired_pos_w = torch.zeros(self.num_envs, 3, device=self.device)
        self.prev_pos_w = torch.zeros(self.num_envs, 3, device=self.device)     # 上一步位置


        # === 日志记录 ===
        # 用于记录每个episode中各项奖励的累积值
        self._episode_sums = {
            key: torch.zeros(self.num_envs, dtype=torch.float, device=self.device)
            for key in [
                "lin_vel",              # 线速度惩罚
                "ang_vel",              # 角速度惩罚
                "ang_vel_final",        # 接近目标时角速度惩罚
                "actions",              # 动作惩罚
                "progress_to_goal",     # 目标进度奖励
                "distance_to_goal",     # 目标距离奖励
                "distance_to_goal_fine", # 精细目标距离奖励
                "undesired_contacts",   # 不期望接触惩罚
                "flat_orientation",     # 姿态惩罚
                "obstacle_proximity",   # 障碍物接近惩罚
                "height_bounds_proximity", # 高度边界惩罚
                "terminated",           # 终止惩罚
            ]
        }

        # === 物理参数计算 ===
        # 获取特定物体的索引和物理属性
        # 获取机体ID
        # self._body_id , _= self._robot.find_bodies("body")  # 四旋翼主体的索引
        self._body_id , _= self._robot.find_bodies("base_link")  # 四旋翼主体的索引
        # 获取不期望接触的身体部件ID
        self._undesired_contact_body_ids = SceneEntityCfg("contact_sensor", body_names=".*").body_ids
        # 计算机器人质量和重量
        self._robot_mass = self._robot.root_physx_view.get_masses()[0].sum()  # 机器人总质量
        self._gravity_magnitude = torch.tensor(self.sim.cfg.gravity, device=self.device).norm()  # 重力加速度大小
        self._robot_weight = (self._robot_mass * self._gravity_magnitude).item()  # 机器人重量

        # 确保显式启用调试可视化
        self.set_debug_vis(False)  # 强制启用调试可视化

    def _setup_scene(self):
        """
        设置仿真场景
        
        创建和配置场景中的所有元素：
        - 机器人（四旋翼）
        - 传感器（接触传感器、激光雷达）
        - 地形
        - 照明
        """
        # 创建四旋翼机器人
        self._robot = Articulation(self.cfg.robot)
        self.scene.articulations["robot"] = self._robot

        # 创建接触传感器
        self._contact_sensor = ContactSensor(self.cfg.contact_sensor)
        self.scene.sensors["contact_sensor"] = self._contact_sensor
        
        # 创建激光雷达传感器
        self._simple_lidar = RayCaster(self.cfg.simple_lidar)
        self.scene.sensors["simple_lidar"] = self._simple_lidar

        # 设置地形
        self.cfg.terrain.num_envs = self.scene.cfg.num_envs
        self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing
        self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)
        
        # 克隆、过滤和复制环境
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[self.cfg.terrain.prim_path])
     
        # 添加光照
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor):
        """
        物理步进前的预处理
        将强化学习动作转换为物理仿真中的推力和力矩。
        Args:
            actions: 智能体输出的动作，形状为 (num_envs, 4)
                    actions[:, 0]: 总推力控制 [-1, 1]
                    actions[:, 1]: 滚转力矩控制 [-1, 1] 
                    actions[:, 2]: 俯仰力矩控制 [-1, 1]
                    actions[:, 3]: 偏航力矩控制 [-1, 1]
        """
        # 限制动作范围到 [-1, 1]
        self._actions = actions.clone().clamp(-2.0, 2.0)
        
        # 将推力动作从 [-1, 1] 映射到 [0, 2*推重比*重量] 范围
        # 这样可以实现从0推力到最大推力的控制
        # new
        # 计算推力：基于推重比和动作值
        # 推力 = 推重比 * 重量 * (缩放因子 * 动作 + 1) / 2
        # 这确保了当动作为0时，推力约等于重量，可以悬停
        self._thrust[:, 0, 2] = self.cfg.thrust_to_weight * self._robot_weight * (self.cfg.thrust_scale * self._actions[:, 0] + 1.0) / 2.0
        # 将力矩动作直接按比例缩放
        self._moment[:, 0, :] = self.cfg.moment_scale * self._actions[:, 1:]

    def _apply_action(self):
        """
        应用动作到仿真环境
        将计算好的推力和力矩应用到四旋翼上。"""
        # 保存当前位置用于计算进度
        self.prev_pos_w = self._robot.data.root_link_pos_w.clone()
        # 施加外力和力矩
        self._robot.set_external_force_and_torque(self._thrust, self._moment, body_ids=self._body_id)

    def _get_observations(self) -> dict:
        # """
        # 获取环境观察值。

        # Returns:
        #     包含策略观察值的字典，观察空间包括：
        #     - 机器人基座坐标系下的线速度 (3维)
        #     - 机器人基座坐标系下的角速度 (3维) 
        #     - 机器人基座坐标系下的重力方向 (3维)
        #     - 机器人基座坐标系下的目标位置 (3维)
        #     总共12维观察空间
        # """
        ## New
        """
        获取环境观察值
        
        构建强化学习智能体的观察空间，包括：
        - 机体线速度和角速度 (3+3维)
        - 重力在机体坐标系的投影 (3维) 
        - 当前高度 (1维)
        - 目标位置在机体坐标系中的位置 (3维)
        - 激光雷达距离数据 (46维)
        
        Returns:
            观察字典，包含策略网络需要的观察值
        """
        # 计算目标位置在机器人基座坐标系下的相对位置
        desired_pos_b, _ = subtract_frame_transforms(
            self._robot.data.root_link_state_w[:, :3],  # 机器人世界位置
            self._robot.data.root_link_state_w[:, 3:7],  # 机器人世界姿态
            self._desired_pos_w  # 目标世界位置
        )
        
        # 添加噪声并归一化
        noisy_desired_pos_b = self._add_uniform_noise(desired_pos_b, -0.02, 0.02)
        noisy_desired_pos_b = (noisy_desired_pos_b / self.cfg.desired_pos_b_obs_clip).clip(-1.0, 1.0)
        # === 获取当前高度并归一化 ===
        height = self._robot.data.root_state_w[:, 2].unsqueeze(1)
        noisy_height = self._add_uniform_noise(height, -0.02, 0.02)
        noisy_height /= self.cfg.height_w_limits[1]
     
        # === 处理激光雷达数据 ===
        # 将激光雷达击中点转换到机体坐标系
        root_state_w = self._robot.data.root_state_w.unsqueeze(1).repeat(1, self._simple_lidar.data.ray_hits_w.shape[1], 1)
        simple_lidar_data_ray_hits_b, _ = subtract_frame_transforms(
            root_state_w[..., :3], root_state_w[..., 3:7], self._simple_lidar.data.ray_hits_w)
        
        # 计算到击中点的距离
        simple_lidar_data_ray_hits_b = torch.nan_to_num(simple_lidar_data_ray_hits_b, nan=float('inf'))
        simple_lidar_data_b = torch.norm(simple_lidar_data_ray_hits_b, dim=-1)
        # 添加噪声并缩放
        noisy_simple_lidar_data_b = self._add_uniform_noise(simple_lidar_data_b, -0.02, 0.02)
        noisy_simple_lidar_data_b = (self.cfg.scaling_lidar_data_b * noisy_simple_lidar_data_b).clip(-1.0, 1.0)
        
        # === 获取机体运动状态并添加噪声 ===
        noisy_root_lin_vel = self._add_uniform_noise(self._robot.data.root_com_lin_vel_b, -0.2, 0.2)
        noisy_root_ang_vel = self._add_uniform_noise(self._robot.data.root_com_ang_vel_b, -0.1, 0.1)
        noisy_projected_gravity_b = self._add_uniform_noise(self._robot.data.projected_gravity_b, -0.03, 0.03)
        

       
        # 拼接所有观察值
        obs = torch.cat(
            [
                noisy_root_lin_vel,         # 机体线速度 (3维)
                noisy_root_ang_vel,         # 机体角速度 (3维)
                noisy_projected_gravity_b,  # 重力投影 (3维)
                noisy_height,               # 归一化高度 (1维)
                noisy_desired_pos_b,        # 目标相对位置 (3维)
                noisy_simple_lidar_data_b,  # 激光雷达数据 (46维)
            ],
            dim=-1,
        )
        observations = {"policy": obs}
        return observations

    def _get_rewards(self) -> torch.Tensor:
        """
        计算奖励函数。

        奖励设计包括三个部分：
        1. 线速度惩罚：鼓励四旋翼稳定飞行，避免剧烈运动
        2. 角速度惩罚：鼓励四旋翼保持稳定姿态
        3. 距离奖励：鼓励四旋翼接近目标位置

        new:
        多目标奖励函数设计，包括：
        - 速度惩罚：鼓励平稳飞行
        - 动作惩罚：鼓励节能
        - 导航奖励：鼓励到达目标
        - 安全惩罚：避免碰撞和不安全行为
        
        Returns:
            每个环境的总奖励张量
        """
        # === 速度相关奖励 ===
        # 线速度惩罚：对前向和侧向速度施加不同权重
        root_lin_vel_processed = self._robot.data.root_com_lin_vel_b.clone()
        root_lin_vel_processed[:, 0] *= torch.where(root_lin_vel_processed[:, 0] < -1.0, 2, 1)  # 后退惩罚加倍
        root_lin_vel_processed[:, 1] *= torch.where(torch.abs(root_lin_vel_processed[:, 1]) > 2.0, 2, 1)  # 侧向高速惩罚
        lin_vel = torch.sum(torch.square(root_lin_vel_processed), dim=1)
        # 软阈值处理
        # 计算线速度的平方和（用于惩罚过快的线性运动）
        # lin_vel = torch.sum(torch.square(self._robot.data.root_lin_vel_b), dim=1)
        lin_vel = torch.where(torch.torch.linalg.norm(root_lin_vel_processed, dim=1) > self.cfg.lin_vel_max_soft_thresh,
                              lin_vel + self.cfg.lin_vel_max_soft_thresh**2 - 2*(lin_vel**0.5)*self.cfg.lin_vel_max_soft_thresh,
                              torch.zeros_like(lin_vel))
        
        # 计算角速度的平方和（用于惩罚过快的旋转运动）
        ang_vel = torch.sum(torch.square(self._robot.data.root_com_ang_vel_b), dim=1)

        # === 动作惩罚 ===
        actions = torch.sum(torch.abs(self._actions), dim=1)

        # === 导航相关奖励 ===
        # 计算到目标的距离
        desired_pos_b, _ = subtract_frame_transforms(
            self._robot.data.root_state_w[:, :3], 
            self._robot.data.root_state_w[:, 3:7], 
            self._desired_pos_w
        )
        
        # 计算到目标的欧几里得距离
        prev_distance_to_goal = torch.linalg.norm(self._desired_pos_w - self.prev_pos_w, dim=1)
        distance_to_goal = torch.linalg.norm(self._desired_pos_w - self._robot.data.root_link_pos_w, dim=1)
        
        # 进度奖励：鼓励朝目标移动
        progress_to_goal_mapped = -torch.tanh((distance_to_goal-prev_distance_to_goal) / (self.cfg.progress_to_goal_std**2))
        
        # 距离奖励：鼓励接近目标
        distance_to_goal_mapped = 1 - torch.tanh(distance_to_goal / (self.cfg.distance_to_goal_std**2))
        distance_to_goal_mapped_fine = 1 - torch.tanh(distance_to_goal / (self.cfg.distance_to_goal_fine_std**2))
        
        # 只有在观察范围内时才给予距离奖励
        desired_pos_b_clipped = (desired_pos_b / self.cfg.desired_pos_b_obs_clip).clip(-1.0, 1.0)
        close_to_goal = torch.logical_and(torch.all(-0.99 < desired_pos_b_clipped, dim=1), torch.all(desired_pos_b_clipped < 0.99, dim=1))
        distance_to_goal_mapped = torch.where(
            close_to_goal, distance_to_goal_mapped, torch.zeros_like(distance_to_goal_mapped))
        distance_to_goal_mapped_fine =torch.where(
            close_to_goal, distance_to_goal_mapped_fine, torch.zeros_like(distance_to_goal_mapped_fine))
        
        # === 安全相关惩罚 ===
        # 接近目标时的角速度惩罚
        ang_vel_final = torch.where(distance_to_goal < self.cfg.ang_vel_final_dist_goal_thresh, ang_vel, torch.zeros_like(ang_vel))
        
        # 不期望接触惩罚
        net_contact_forces = self._contact_sensor.data.net_forces_w_history
        is_contact = (
            torch.max(torch.norm(net_contact_forces[:, :, self._undesired_contact_body_ids], dim=-1), dim=1)[0] > 0.01
        )
        undesired_contacts = torch.sum(is_contact, dim=1)
        
        # 姿态惩罚：鼓励保持水平
        flat_orientation = torch.sum(torch.square(self._robot.data.projected_gravity_b[:, :2]), dim=1)
        
        # 障碍物接近惩罚
        root_state_w = self._robot.data.root_state_w.unsqueeze(1).repeat(1, self._simple_lidar.data.ray_hits_w.shape[1], 1)
        simple_lidar_data_ray_hits_b, _ = subtract_frame_transforms(
            root_state_w[..., :3],root_state_w[..., 3:7], self._simple_lidar.data.ray_hits_w)
        simple_lidar_data_ray_hits_b = torch.nan_to_num(simple_lidar_data_ray_hits_b, nan=float('inf'))
        simple_lidar_data_b = torch.norm(simple_lidar_data_ray_hits_b, dim=-1)
        obstacle_proximity = torch.max(torch.where(simple_lidar_data_b < self.cfg.threshold_obstacle_proximity,
                                         self.cfg.threshold_obstacle_proximity - simple_lidar_data_b,
                                         torch.zeros_like(simple_lidar_data_b)), dim=1)[0]
        
        # 高度边界接近惩罚
        height_low_bound_proximity = torch.where(self._robot.data.root_state_w[:, 2] < self.cfg.height_w_soft_limits[0],
                                                self.cfg.height_w_soft_limits[0] - self._robot.data.root_state_w[:, 2],
                                                torch.zeros_like(self._robot.data.root_state_w[:, 2]))
        height_high_bound_proximity = torch.where(self._robot.data.root_state_w[:, 2] > self.cfg.height_w_soft_limits[1],
                                                self._robot.data.root_state_w[:, 2] - self.cfg.height_w_soft_limits[1],
                                                torch.zeros_like(self._robot.data.root_state_w[:, 2]))
        height_bounds_proximity = torch.max(height_low_bound_proximity, height_high_bound_proximity)
        
        # === 组合所有奖励项 ===
        rewards = {
            "lin_vel": lin_vel * self.cfg.lin_vel_reward_scale * self.step_dt,
            "ang_vel": ang_vel * self.cfg.ang_vel_reward_scale * self.step_dt,
            "ang_vel_final": ang_vel_final * self.cfg.ang_vel_final_reward_scale * self.step_dt,
            "actions": actions * self.cfg.actions_reward_scale * self.step_dt,
            "progress_to_goal": progress_to_goal_mapped * self.cfg.progress_to_goal_reward_scale * self.step_dt,
            "distance_to_goal": distance_to_goal_mapped * self.cfg.distance_to_goal_reward_scale * self.step_dt,
            "distance_to_goal_fine": distance_to_goal_mapped_fine * self.cfg.distance_to_goal_fine_reward_scale * self.step_dt,
            "undesired_contacts": undesired_contacts * self.cfg.undesired_contacts_reward_scale * self.step_dt,
            "flat_orientation": flat_orientation * self.cfg.flat_orientation_reward_scale * self.step_dt,
            "obstacle_proximity": obstacle_proximity * self.cfg.obstacle_proximity_reward_scale * self.step_dt,
            "height_bounds_proximity": height_bounds_proximity * self.cfg.height_bounds_proximity_reward_scale * self.step_dt,
            "terminated": self.reset_terminated * self.cfg.terminated_reward_scale * self.step_dt,
        }
        # 计算总奖励
        reward = torch.sum(torch.stack(list(rewards.values())), dim=0)
        
        # 记录奖励用于日志
        for key, value in rewards.items():
            self._episode_sums[key] += value
        return reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """
        检查回合结束条件。

        Returns:
            tuple: (died, time_out)
            - died: 因异常情况死亡的环境掩码（如碰撞地面或飞行过高）
            - time_out: 因达到最大步数而超时的环境掩码
        """
        # 检查是否达到最大步数(超时判断)
        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # 超出高度边界
        out_of_height_bounds = torch.logical_or(
                self._robot.data.root_link_pos_w[:, 2] < self.cfg.height_w_limits[0],
                self._robot.data.root_link_pos_w[:, 2] > self.cfg.height_w_limits[1])
        
        # 碰撞检测
        net_contact_forces = self._contact_sensor.data.net_forces_w_history
        collided = torch.any(torch.max(torch.norm(net_contact_forces[:, :, self._undesired_contact_body_ids],
                                           dim=-1), dim=1)[0] > 0.0001, dim=1)
        
        # 失败终止条件
        died = torch.logical_or(out_of_height_bounds, collided)
        return died, time_out

    def _add_uniform_noise(self, data: torch.Tensor, min_noise: float, max_noise: float):
        """
        添加均匀分布噪声
        
        用于增强观察值的鲁棒性，模拟传感器噪声。
        
        Args:
            data: 输入数据
            min_noise: 最小噪声值
            max_noise: 最大噪声值
            
        Returns:
            添加噪声后的数据
        """
        return data + torch.rand_like(data) * (max_noise - min_noise) + min_noise


    def _sample_points_square_hole(self, n: int, side_length: float, hole_length: float):
        """
        在带孔正方形区域中采样点
        
        避免目标点生成在地形中心的空洞区域，确保目标可达。
        
        Args:
            n: 采样点数量
            side_length: 正方形边长
            hole_length: 中心孔洞边长
            
        Returns:
            采样得到的2D点坐标
        """
        indices = torch.arange(n, device=self.device)
        shuffled_indices = indices[torch.randperm(n)]
        
        # 将采样点分成4个区域
        split_sizes = [n // 4] * 4
        for i in range(n % 4):
            split_sizes[i] += 1  # 将余数分配给前几个分割
        
        idx_splits = torch.split(shuffled_indices, split_sizes)
        
        points = torch.empty(n, 2, device=self.device)
        half_s, half_h = side_length / 2, hole_length / 2

        # 在每个区域中采样
        # 区域1：上方矩形
        points[idx_splits[0]] = torch.stack([
            torch.empty(len(idx_splits[0]), device=self.device).uniform_(-half_h, half_s),       
            torch.empty(len(idx_splits[0]), device=self.device).uniform_(half_h, half_s)         
        ], dim=1)
        
        # 区域2：下方矩形
        points[idx_splits[1]] = torch.stack([
            torch.empty(len(idx_splits[1]), device=self.device).uniform_(-half_s, half_h),
            torch.empty(len(idx_splits[1]), device=self.device).uniform_(-half_s, -half_h)       
        ], dim=1)
        
        # 区域3：左侧矩形
        points[idx_splits[2]] = torch.stack([
            torch.empty(len(idx_splits[2]), device=self.device).uniform_(-half_s, -half_h),      
            torch.empty(len(idx_splits[2]), device=self.device).uniform_(-half_h, half_s)        
        ], dim=1)
        
        # 区域4：右侧矩形
        points[idx_splits[3]] = torch.stack([
            torch.empty(len(idx_splits[3]), device=self.device).uniform_(half_h, half_s), 
            torch.empty(len(idx_splits[3]), device=self.device).uniform_(-half_s, half_h)
        ], dim=1)
        
        return points
    
    def _reset_idx(self, env_ids: torch.Tensor | None):
        """
        重置指定的环境。
        当环境终止时重新初始化机器人状态、目标位置和传感器数据。
        Args:
            env_ids: 需要重置的环境ID，如果为None则重置所有环境
        """
        if env_ids is None or len(env_ids) == self.num_envs:
            env_ids = self._robot._ALL_INDICES

        # === 性能统计和日志记录 ===
        distances_to_goal = torch.linalg.norm(
            self._desired_pos_w[env_ids] - self._robot.data.root_link_pos_w[env_ids], dim=1
        )
        final_distance_to_goal = distances_to_goal.mean()
        final_distance_to_goal_10_percentile = torch.quantile(distances_to_goal, 0.10, interpolation="linear")
        final_distance_to_goal_90_percentile = torch.quantile(distances_to_goal, 0.90, interpolation="linear")
    
        # 计算表现最好10%环境的角速度
        idxs = torch.argwhere(distances_to_goal <= final_distance_to_goal_10_percentile + 1e-4).flatten()
        final_ang_vel_distance_to_goal_10_percentile = torch.abs(self._robot.data.root_com_ang_vel_b[idxs]).mean()
        
        # 记录回合奖励统计
        extras = dict()
        for key in self._episode_sums.keys():
            episodic_sum_avg = torch.mean(self._episode_sums[key][env_ids])
            extras["Episode_Reward/" + key] = episodic_sum_avg / self.max_episode_length_s
            self._episode_sums[key][env_ids] = 0.0
        self.extras["log"] = dict()
        self.extras["log"].update(extras)
        
        # 记录终止原因统计
        extras = dict()
        extras["Episode_Termination/died"] = torch.count_nonzero(self.reset_terminated[env_ids]).item()
        extras["Episode_Termination/time_out"] = torch.count_nonzero(self.reset_time_outs[env_ids]).item()
        extras["Metrics/final_distance_to_goal"] = final_distance_to_goal.item()
        extras["Metrics/final_distance_to_goal_10_percentile"] = final_distance_to_goal_10_percentile.item()
        extras["Metrics/final_distance_to_goal_90_percentile"] = final_distance_to_goal_90_percentile.item()
        extras["Metrics/final_ang_vel_goal_10_percentile"] = final_ang_vel_distance_to_goal_10_percentile.item()
        self.extras["log"].update(extras)

        # 重置机器人状态
        self._robot.reset(env_ids)
        super()._reset_idx(env_ids)
        
        # 避免训练时的重置峰值：随机化episode长度
        if self.cfg.avoid_reset_spikes_in_training and len(env_ids) == self.num_envs:
            self.episode_length_buf = torch.randint_like(self.episode_length_buf, high=int(self.max_episode_length))

        # 重置动作
        self._actions[env_ids] = 0.0
        
        # === 采样新的目标位置 ===
        # 在xy平面上随机采样目标位置 [-2, 2] 米范围
        # self._desired_pos_w[env_ids, :2] = torch.zeros_like(self._desired_pos_w[env_ids, :2]).uniform_(-2.0, 2.0)
        self._desired_pos_w[env_ids, :2] = self._sample_points_square_hole(len(env_ids),
            2*self.cfg.desired_pos_b_xy_limits[1], 2*self.cfg.desired_pos_b_xy_limits[0])
        
        # 随机地形重生（如果启用）
        if self.cfg.random_respawn:
            rand_terrain_value = torch.rand(len(env_ids), device=self.device)
            move_up = rand_terrain_value < 0.33
            move_down = rand_terrain_value > 0.66
            self._terrain.update_env_origins(env_ids, move_up, move_down)
           
        # 加上环境原点偏移,调整目标位置到地形坐标系
        self._desired_pos_w[env_ids, :2] += self._terrain.env_origins[env_ids, :2]
        # 在z轴上随机采样目标高度 [0.5, 1.5] 米范围
        # self._desired_pos_w[env_ids, 2] = torch.zeros_like(self._desired_pos_w[env_ids, 2]).uniform_(0.5, 1.5)
        self._desired_pos_w[env_ids, 2] = self._desired_pos_w[env_ids, 2].uniform_(
                self.cfg.desired_pos_w_height_limits[0], self.cfg.desired_pos_w_height_limits[1])
        
        # === 重置机器人初始状态 ===
        joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_vel = self._robot.data.default_joint_vel[env_ids]
        default_root_state = self._robot.data.default_root_state[env_ids]

        # 随机化初始高度
        default_root_state[:, 2] = (2*torch.rand_like(default_root_state[:, 2])-1) * \
            0.25*(self.cfg.height_w_limits[1] - self.cfg.height_w_limits[0]) + \
                (self.cfg.height_w_limits[1] - self.cfg.height_w_limits[0]) / 2.0
        
        # 调整位置到地形坐标系
        default_root_state[:, :2] += self._terrain.env_origins[env_ids, :2]

        # 随机化初始偏航角
        default_root_state[:, 3:7] = random_yaw_orientation(default_root_state.shape[0], device=self.device)
        
        # 将状态写入仿真
        self._robot.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self._robot.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

    def _set_debug_vis_impl(self, debug_vis: bool):
        """
        设置调试可视化的具体实现。
        创建或隐藏目标位置和机器人位置的可视化标记。

        Args:
            debug_vis: 是否启用调试可视化
        """
        # 如果需要调试可视化，创建位置可视化标记
        if debug_vis:
            from isaaclab.sim import PreviewSurfaceCfg
            # 目标位置可视化
            if not hasattr(self, "goal_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.1, 0.1, 0.1)  # 增大立方体标记尺寸
                # 设置目标位置可视化路径
                marker_cfg.prim_path = "/Visuals/Command/goal_position"
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)
            self.goal_pos_visualizer.set_visibility(True)
            
            # 机器人位置可视化
            if not hasattr(self, "robot_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.1, 0.1, 0.1)  # 大立方体标记
                marker_cfg.markers["cuboid"].visual_material = PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0), metallic=0.2)
                # 设置机器人位置可视化路径
                marker_cfg.prim_path = "/Visuals/Command/robot_position"
                self.robot_pos_visualizer = VisualizationMarkers(marker_cfg)
            self.robot_pos_visualizer.set_visibility(True)
        else:
            # 隐藏可视化标记
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)
            if hasattr(self, "robot_pos_visualizer"):
                self.robot_pos_visualizer.set_visibility(False)
                
    def _debug_vis_callback(self, event):
        """
        调试可视化回调函数，在每个渲染帧更新可视化标记。

        Args:
            event: 回调事件
        """
        # 更新目标位置标记的显示位置
        self.goal_pos_visualizer.visualize(self._desired_pos_w)
        
        # 更新机器人位置标记的显示位置
        if hasattr(self, "robot_pos_visualizer"):
            self.robot_pos_visualizer.visualize(self._robot.data.root_pos_w)

class CrazyFlieEnvCfg_PLAY(CrazyFlieEnvCfg):
    """
    四旋翼环境演示配置类
    
    基于训练配置，但调整了参数以适合演示和测试：
    - 更长的episode时间
    - 更大的地形
    - 更高的难度
    - 启用随机重生
    """
    
    def __post_init__(self):
        """初始化后处理，调整演示参数"""
        # 调用父类的后初始化
        super().__post_init__()
    
        # === 演示环境调整 ===
        self.episode_length_s *= 12  # 4倍episode长度，允许更长时间的探索
        self.size_terrain *= 4      # 4倍地形大小，提供更大的活动空间
        self.objects_density_min *= 1    # 保持最小障碍物密度
        self.objects_density_max *= 0.8  # 降低最大障碍物密度，提供更多飞行空间
        
        # 调整目标位置范围以适应新的地形大小
        self.desired_pos_b_xy_limits = (self.size_terrain/2-1.0, self.size_terrain/2)
        
        # 启用高级功能
        self.random_respawn = True  # 启用随机地形重生
        self.avoid_reset_spikes_in_training = False  # 禁用训练优化，因为这是演示环境
        
        # === 更新地形配置 ===
        self.terrain = TerrainImporterCfg(
            prim_path="/World/ground",
            terrain_type="generator",
            terrain_generator=QUADCOPTER_ROUGH_TERRAINS_CFG_FACTORY(
                size=self.size_terrain, 
                density_min=self.objects_density_min,
                density_max=self.objects_density_max, 
                num_rows=1,   # 5x5的地形网格
                num_cols=1
            ),
            max_init_terrain_level=None,
            collision_group=-1,
            physics_material=sim_utils.RigidBodyMaterialCfg(
                friction_combine_mode="multiply",
                restitution_combine_mode="multiply",
                static_friction=1.0,
                dynamic_friction=1.0,
                restitution=0.0,
            ),
            visual_material=sim_utils.MdlFileCfg(
                mdl_path=f"{NVIDIA_NUCLEUS_DIR}/Materials/Base/Architecture/Shingles_01.mdl",
                project_uvw=True,
            ),
            debug_vis=True,
        )
    def _set_debug_vis_impl(self, debug_vis: bool):
        """
        设置调试可视化的具体实现。
        创建或隐藏目标位置和机器人位置的可视化标记。

        Args:
            debug_vis: 是否启用调试可视化
        """
        # 如果需要调试可视化，创建位置可视化标记
        if debug_vis:
            from isaaclab.sim import PreviewSurfaceCfg
            # 目标位置可视化
            if not hasattr(self, "goal_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.1, 0.1, 0.1)  # 增大立方体标记尺寸
                # 设置目标位置可视化路径
                marker_cfg.prim_path = "/Visuals/Command/goal_position"
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)
            self.goal_pos_visualizer.set_visibility(True)
            
            # 机器人位置可视化
            if not hasattr(self, "robot_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.1, 0.1, 0.1)  # 大立方体标记
                marker_cfg.markers["cuboid"].visual_material = PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0), metallic=0.2)
                # 设置机器人位置可视化路径
                marker_cfg.prim_path = "/Visuals/Command/robot_position"
                self.robot_pos_visualizer = VisualizationMarkers(marker_cfg)
            self.robot_pos_visualizer.set_visibility(True)
        else:
            # 隐藏可视化标记
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)
            if hasattr(self, "robot_pos_visualizer"):
                self.robot_pos_visualizer.set_visibility(False)