# -*- coding: utf-8 -*-
"""
二维浅水波动仿真程序
使用有限差分法求解浅水方程组

创建时间: 2018年2月17日 11:26:20
作者: Chi

该程序实现了二维浅水波动的数值仿真，基于浅水方程组：
∂h/∂t + ∂(hu)/∂x + ∂(hv)/∂y = 0                    (连续性方程)
∂u/∂t + u∂u/∂x + v∂u/∂y + g∂h/∂x = 0              (x方向动量方程)
∂v/∂t + u∂v/∂x + v∂v/∂y + g∂h/∂y = 0              (y方向动量方程)

其中：
h - 水深 (m)
u - x方向流速 (m/s)
v - y方向流速 (m/s)
g - 重力加速度 (m/s²)
"""

# =============================================================================
# 导入必要的库和初始化参数
# =============================================================================

import numpy as np

# 仿真参数设置
n = 15      # 网格密度 (网格数量为n×n)
g = 9.8     # 重力加速度 (m/s²)
dt = 0.04   # 时间步长 (s) - 注释中的"running velocity"应为时间步长
dx = 1.0    # x方向网格间距 (m)
dy = 1.0    # y方向网格间距 (m)

# 主要物理量数组初始化 (包含边界，大小为(n+2)×(n+2))
h = np.ones((n+2,n+2))    # 水深场，初始化为1.0米
u = np.zeros((n+2,n+2))   # x方向速度场，初始化为0
v = np.zeros((n+2,n+2))   # y方向速度场，初始化为0

# x方向半步长点的物理量 (用于Lax-Wendroff格式)
hx = np.zeros((n+1,n+1))  # x方向半步长点的水深
ux = np.zeros((n+1,n+1))  # x方向半步长点的u速度
vx = np.zeros((n+1,n+1))  # x方向半步长点的v速度

# y方向半步长点的物理量 (用于Lax-Wendroff格式)
hy = np.zeros((n+1,n+1))  # y方向半步长点的水深
uy = np.zeros((n+1,n+1))  # y方向半步长点的u速度
vy = np.zeros((n+1,n+1))  # y方向半步长点的v速度

# 仿真控制参数
nsteps = 0      # 时间步数计数器
h[1,1] = 0.5    # 在(1,1)位置设置初始扰动，水深为0.5米（产生波源）

def reflective():
    """
    反射边界条件函数

    在计算域边界处施加反射边界条件：
    - 水深h：在边界处使用内部相邻点的值（零梯度条件）
    - 速度u,v：在相应边界处使用反射条件
      * 在垂直边界（左右）：u取相反值，v保持相同值
      * 在水平边界（上下）：v取相反值，u保持相同值

    这种边界条件模拟了波在固体壁面的反射现象
    """
    # 水深边界条件：零梯度（Neumann边界条件）
    h[:,0] = h[:,1]      # 左边界：h(0,j) = h(1,j)
    h[:,n+1] = h[:,n]    # 右边界：h(n+1,j) = h(n,j)
    h[0,:] = h[1,:]      # 下边界：h(i,0) = h(i,1)
    h[n+1,:] = h[n,:]    # 上边界：h(i,n+1) = h(i,n)

    # u速度边界条件
    u[:,0] = u[:,1]      # 左边界：u分量保持连续
    u[:,n+1] = u[:,n]    # 右边界：u分量保持连续
    u[0,:] = -u[1,:]     # 下边界：u分量反射（取相反值）
    u[n+1,:] = -u[n,:]   # 上边界：u分量反射（取相反值）

    # v速度边界条件
    v[:,0] = -v[:,1]     # 左边界：v分量反射（取相反值）
    v[:,n+1] = -v[:,n]   # 右边界：v分量反射（取相反值）
    v[0,:] = v[1,:]      # 下边界：v分量保持连续
    v[n+1,:] = v[n,:]    # 上边界：v分量保持连续

def proses():
    """
    主要的数值计算函数 - 使用Lax-Wendroff有限差分格式求解浅水方程组

    该函数实现了二步Lax-Wendroff格式：
    1. 第一步：计算半时间步长、半空间步长处的物理量
    2. 第二步：使用半步长处的值更新下一时间步的物理量

    求解的方程组：
    ∂h/∂t + ∂(hu)/∂x + ∂(hv)/∂y = 0
    ∂u/∂t + u∂u/∂x + v∂u/∂y + g∂h/∂x = 0
    ∂v/∂t + u∂v/∂x + v∂v/∂y + g∂h/∂y = 0

    返回:
        tuple: 更新后的(h, u, v)数组
    """

    # 第一步：计算x方向半步长点的物理量
    # 使用Lax-Wendroff格式的预测步
    for i in range(n+1):
        for j in range(n):
            # x方向半步长点的水深
            hx[i,j] = (h[i+1,j+1]+h[i,j+1])/2 - dt/(2*dx)*(u[i+1,j+1]-u[i,j+1])

            # x方向半步长点的u速度（包含压力梯度和对流项）
            ux[i,j] = (u[i+1,j+1]+u[i,j+1])/2 - dt/(2*dx)*((pow(u[i+1,j+1],2)/h[i+1,j+1] + g/2*pow(h[i+1,j+1],2)) - (pow(u[i,j+1],2)/h[i,j+1] + g/2*pow(h[i,j+1],2)))

            # x方向半步长点的v速度（对流项）
            vx[i,j] = (v[i+1,j+1]+v[i,j+1])/2 - dt/(2*dx)*((u[i+1,j+1]*v[i+1,j+1]/h[i+1,j+1]) - (u[i,j+1]*v[i,j+1]/h[i,j+1]))

    # 第一步：计算y方向半步长点的物理量
    for i in range(n):
        for j in range(n+1):
            # y方向半步长点的水深
            hy[i,j] = (h[i+1,j+1]+h[i+1,j])/2 - dt/(2*dy)*(v[i+1,j+1]-v[i+1,j])

            # y方向半步长点的u速度（对流项）
            uy[i,j] = (u[i+1,j+1]+u[i+1,j])/2 - dt/(2*dy)*((v[i+1,j+1]*u[i+1,j+1]/h[i+1,j+1]) - (v[i+1,j]*u[i+1,j]/h[i+1,j]))

            # y方向半步长点的v速度（包含压力梯度和对流项）
            vy[i,j] = (v[i+1,j+1]+v[i+1,j])/2 - dt/(2*dy)*((pow(v[i+1,j+1],2)/h[i+1,j+1] + g/2*pow(h[i+1,j+1],2)) - (pow(v[i+1,j],2)/h[i+1,j] + g/2*pow(h[i+1,j],2)))

    # 第二步：使用半步长处的值更新下一时间步的物理量（校正步）
    for i in range(1,n+1):
        for j in range(1,n+1):
            # 更新水深（连续性方程）
            h[i,j] = h[i,j] - (dt/dx)*(ux[i,j-1]-ux[i-1,j-1]) - (dt/dy)*(vy[i-1,j]-vy[i-1,j-1])

            # 更新u速度（x方向动量方程）
            u[i,j] = u[i,j] - (dt/dx)*((pow(ux[i,j-1],2)/hx[i,j-1] + g/2*pow(hx[i,j-1],2)) - (pow(ux[i-1,j-1],2)/hx[i-1,j-1] + g/2*pow(hx[i-1,j-1],2))) - (dt/dy)*((vy[i-1,j]*uy[i-1,j]/hy[i-1,j]) - (vy[i-1,j-1]*uy[i-1,j-1]/hy[i-1,j-1]))

            # 更新v速度（y方向动量方程）
            v[i,j] = v[i,j] - (dt/dx)*((ux[i,j-1]*vx[i,j-1]/hx[i,j-1]) - (ux[i-1,j-1]*vx[i-1,j-1]/hx[i-1,j-1])) - (dt/dy)*((pow(vy[i-1,j],2)/hy[i-1,j] + g/2*pow(hy[i-1,j],2)) - (pow(vy[i-1,j-1],2)/hy[i-1,j-1] + g/2*pow(hy[i-1,j-1],2)))

    # 应用边界条件
    reflective()
    return h,u,v

# =============================================================================
# 注释掉的测试代码 - 可用于静态测试
# =============================================================================
'''
# 运行17个时间步的静态测试
for i in range (17):
    #print h  # 可以打印水深场查看变化
    proses()  # 注意：原代码中proses(1)的参数1是多余的，函数不需要参数
'''

# =============================================================================
# 三维可视化设置
# =============================================================================

# 导入绘图相关库
import matplotlib.pyplot as plt
from matplotlib import cm  # 颜色映射
from matplotlib.ticker import LinearLocator, FormatStrFormatter  # 坐标轴格式化
from mpl_toolkits.mplot3d import Axes3D  # 3D绘图工具（虽然未直接使用但需要导入）

# 可视化参数设置
a = n  # 坐标轴范围，与网格大小一致

# 创建坐标网格用于绘图
x = np.arange(n+2)  # x坐标数组，从0到n+1
y = np.arange(n+2)  # y坐标数组，从0到n+1
x, y = np.meshgrid(x, y)  # 创建二维坐标网格

# 创建图形窗口和3D坐标轴
fig = plt.figure()  # 创建新的图形窗口
ax = fig.add_subplot(111, projection='3d')  # 添加3D子图

def plotset():
    """
    设置3D绘图的基本参数和等高线

    该函数设置：
    - 3D坐标轴的范围和格式
    - 在三个坐标平面上绘制等高线投影
    - z轴的刻度和格式
    """
    # 设置3D坐标轴范围
    ax.set_xlim3d(0, a)      # x轴范围：0到网格大小
    ax.set_ylim3d(0, a)      # y轴范围：0到网格大小
    ax.set_zlim3d(0.5, 1.5)  # z轴范围：0.5到1.5米（水深范围）
    ax.set_autoscalez_on(False)  # 禁用z轴自动缩放

    # 设置z轴刻度和格式
    ax.zaxis.set_major_locator(LinearLocator(10))  # z轴主刻度定位器，10个刻度
    ax.zaxis.set_major_formatter(FormatStrFormatter('%.02f'))  # z轴数字格式，保留2位小数

    # 在三个坐标平面上绘制等高线投影
    cset = ax.contour(x, y, h, zdir='x', offset=0, cmap=cm.coolwarm)  # 投影到x=0平面（yz平面）
    cset = ax.contour(x, y, h, zdir='y', offset=n, cmap=cm.coolwarm)  # 投影到y=n平面（xz平面）
    cset = ax.contour(x, y, h, zdir='z', offset=0.5, cmap=cm.coolwarm)  # 投影到z=0.5平面（xy平面）

# 初始化绘图设置
plotset()

# 绘制初始的3D水面
surf = ax.plot_surface(x, y, h,           # 坐标和高度数据
                      rstride=1,          # 行采样步长
                      cstride=1,          # 列采样步长
                      cmap=cm.coolwarm,   # 颜色映射（蓝色到红色）
                      linewidth=0,        # 网格线宽度为0（无网格线）
                      antialiased=False,  # 禁用抗锯齿
                      alpha=0.7)          # 透明度为0.7

# 添加颜色条
fig.colorbar(surf, shrink=0.5, aspect=5)  # shrink=0.5缩小颜色条，aspect=5设置长宽比

# =============================================================================
# 动画设置和播放
# =============================================================================

# 导入动画模块
from matplotlib import animation

def data(k, h, surf):
    """
    动画更新函数

    该函数在每一帧动画中被调用，用于：
    1. 执行一步数值计算（更新物理场）
    2. 清除当前图形
    3. 重新设置绘图参数
    4. 绘制新的水面形状

    参数:
        k: 帧数索引（在当前实现中未使用）
        h: 水深数组（全局变量，会被proses()函数更新）
        surf: 表面对象（用于返回，虽然实际上会被重新创建）

    返回:
        tuple: 包含新表面对象的元组（用于动画系统）
    """
    # 执行一步数值计算，更新h, u, v场
    proses()

    # 清除当前的3D图形内容
    ax.clear()

    # 重新设置绘图参数和等高线
    plotset()

    # 重新绘制更新后的3D水面
    surf = ax.plot_surface(x, y, h,           # 使用更新后的水深数据
                          rstride=1,          # 行采样步长
                          cstride=1,          # 列采样步长
                          cmap=cm.coolwarm,   # 颜色映射
                          linewidth=0,        # 无网格线
                          antialiased=False,  # 禁用抗锯齿
                          alpha=0.7)          # 透明度

    return surf,  # 返回表面对象（注意逗号，返回元组）

# 创建动画对象
ani = animation.FuncAnimation(
    fig,                    # 图形窗口
    data,                   # 更新函数
    fargs=(h, surf),        # 传递给更新函数的额外参数
    interval=10,            # 帧间隔时间（毫秒），10ms = 100fps
    blit=False              # 禁用blitting优化（因为使用了ax.clear()）
)

# 可选：保存动画为视频文件
# ani.save('water_wave_simulation.mp4', bitrate=512)  # 取消注释以保存视频

# 显示动画窗口
plt.show()

# =============================================================================
# 程序说明总结
# =============================================================================
"""
本程序的主要功能：

1. 数值方法：
   - 使用Lax-Wendroff有限差分格式求解二维浅水方程组
   - 二步预测-校正方法，具有二阶精度
   - 反射边界条件模拟固体壁面

2. 物理模型：
   - 浅水方程组描述水波在浅水中的传播
   - 考虑重力、惯性和连续性效应
   - 适用于波长远大于水深的情况

3. 可视化：
   - 实时3D动画显示水面高度变化
   - 等高线投影显示波的传播模式
   - 颜色映射直观显示水深变化

4. 初始条件：
   - 在(1,1)位置设置点源扰动
   - 其余区域初始水深为1.0米
   - 初始速度为零

运行程序后，可以观察到：
- 从点源产生的圆形波向外传播
- 波在边界处发生反射
- 反射波与入射波相互作用形成复杂的波形
"""