import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm
from matplotlib.ticker import LinearLocator, FormatStrFormatter
from matplotlib import animation

# =============================================================================
# 1. 仿真和物理参数设置 (符合论文模型)
# =============================================================================
n = 50      # 网格密度 (增加密度以获得更平滑的波形)
g = 9.8     # 重力加速度 (m/s²)
dt = 0.04   # 动画的时间步长 (s)
dx = 0.5    # x方向网格间距 (m)
dy = 0.5    # y方向网格间距 (m)
L_x = n * dx # 模拟区域的X方向总长度
L_y = n * dy # 模拟区域的Y方向总长度

# 初始水深 (论文模型中这是一个基准，而不是变量)
initial_depth = 5.0

# 物理量数组初始化
# h 现在只存储波面高度，它将在每一帧被完全重算
h = np.zeros((n, n))

# =============================================================================
# 2. 论文波浪模型的核心实现 (替换原来的 'proses' 函数)
# =============================================================================

# 定义构成海况的规则波参数 (可以随意添加/修改)
# 这是一个列表，每个元素是一个描述波的字典
# 'A': 振幅, 'omega': 角频率, 'direction': 传播方向 ('x'或'y'), 'phase': 初始相位
wave_components = [
    # X方向的波: 一个低频涌浪 + 一个高频风浪
    {'A': 0.1,  'omega': 1.0, 'direction': 'x', 'phase': 0},
    {'A': 0.03, 'omega': 10.0, 'direction': 'x', 'phase': np.pi/2},
    # Y方向的波: 一个中频波
    {'A': 0.07, 'omega': 2.0, 'direction': 'y', 'phase': np.pi/4},
    {'A': 0.04, 'omega': 7.0, 'direction': 'y', 'phase': 0},
]

# 预计算每个波的波数 k (根据论文中的色散关系: ω² = g*k)
for wave in wave_components:
    wave['k'] = wave['omega']**2 / g

# 创建坐标网格 (用于矢量化计算)
x_coords = np.arange(0, n) * dx
y_coords = np.arange(0, n) * dy
X, Y = np.meshgrid(x_coords, y_coords)

def update_wave_surface(t):
    """
    根据论文的解析解，直接计算在时间 t 的完整波面。
    这是新的“计算引擎”。
    
    参数:
        t (float): 当前的模拟时间
        
    返回:
        np.ndarray: 包含当前时刻波面高度的2D数组
    """
    # 从基准水深开始
    current_h = np.full((n, n), initial_depth)
    
    # 线性叠加所有波浪分量
    for wave in wave_components:
        A = wave['A']
        k = wave['k']
        omega = wave['omega']
        phase = wave['phase']
        
        if wave['direction'] == 'x':
            # 计算X方向传播的波对整个平面的影响
            current_h += A * np.cos(k * X - omega * t + phase)
        elif wave['direction'] == 'y':
            # 计算Y方向传播的波对整个平面的影响
            current_h += A * np.cos(k * Y - omega * t + phase)
            
    return current_h

# =============================================================================
# 3. 三维可视化设置 (基本沿用您的代码)
# =============================================================================

# 创建图形窗口和3D坐标轴
fig = plt.figure(figsize=(12, 8)) # 增大图形窗口
ax = fig.add_subplot(111, projection='3d')

def plotset(current_h):
    """
    设置3D绘图的基本参数。
    """
    # 设置坐标轴范围
    ax.set_xlim(0, L_x)
    ax.set_ylim(0, L_y)
    # 动态调整Z轴范围以适应波浪起伏
    z_min = initial_depth - 0.5
    z_max = initial_depth + 0.5
    ax.set_zlim(z_min, z_max)
    ax.set_autoscalez_on(False)
    
    # 设置坐标轴标签
    ax.set_xlabel("X-axis (m)")
    ax.set_ylabel("Y-axis (m)")
    ax.set_zlabel("Wave Height (m)")

# =============================================================================
# 4. 动画设置和播放 (修改 'data' 函数以使用新引擎)
# =============================================================================

# 初始化一个空的表面对象，它将在动画中被更新
# 注意：X, Y现在的大小是 n×n，与 h 匹配
surf = ax.plot_surface(X, Y, h, cmap=cm.coolwarm, linewidth=0, antialiased=False)

def animate_frame(frame_num):
    """
    动画更新函数 - 这是连接计算和可视化的桥梁
    """
    global surf # 允许我们修改全局的surf对象
    
    # 1. 计算当前时间
    current_time = frame_num * dt
    
    # 2. 调用新的计算引擎，直接获得当前时刻的波面
    h_new = update_wave_surface(current_time)
    
    # 3. 更新图形
    ax.clear()          # 清除旧的图形
    plotset(h_new)      # 重新设置坐标轴等
    
    # 绘制新的表面
    surf = ax.plot_surface(X, Y, h_new, cmap=cm.ocean, # 换个海洋色
                          rstride=1, cstride=1,
                          linewidth=0, antialiased=False, alpha=0.9)
    
    # 在标题中显示时间
    ax.set_title(f"Linear Wave Superposition (Paper's Model)\nTime: {current_time:.2f} s")
    
    return surf,

# 创建动画对象
# 注意：我们不再需要 fargs，因为 animate_frame 自己计算时间和波高
ani = animation.FuncAnimation(
    fig,
    animate_frame,
    frames=500,        # 总共播放500帧
    interval=1,       # 帧间隔时间 (ms)
    blit=False
)

# 显示动画窗口
plt.show()