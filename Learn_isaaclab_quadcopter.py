#!/usr/bin/env python3
"""
四旋翼无人机强化学习环境 - Isaac Lab实现

这个文件实现了一个基于Isaac Lab的四旋翼无人机强化学习环境，用于训练无人机的飞行控制策略。
环境包含了完整的物理仿真、奖励函数设计、观测空间定义和动作空间设计。

主要功能：
1. 四旋翼无人机的物理仿真（基于Crazyflie模型）
2. 目标位置跟踪任务
3. 强化学习环境接口（符合Gymnasium标准）
4. 可视化调试工具
5. 多环境并行仿真支持

作者: Isaac Lab Project Developers
许可证: BSD-3-Clause
版权: (c) 2022-2025
"""

# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

# ========================================
# 标准库导入
# ========================================
import gymnasium as gym  # OpenAI Gym接口，用于强化学习环境标准化
import torch  # PyTorch深度学习框架，用于张量计算和GPU加速

# ========================================
# Isaac Lab核心模块导入
# ========================================
import isaaclab.sim as sim_utils  # 仿真工具模块，包含物理材料、光照等配置
from isaaclab.assets import Articulation, ArticulationCfg  # 关节机器人资产管理
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg  # 直接强化学习环境基类
from isaaclab.envs.ui import BaseEnvWindow  # 环境UI窗口基类
from isaaclab.markers import VisualizationMarkers  # 可视化标记工具
from isaaclab.scene import InteractiveSceneCfg  # 交互式场景配置
from isaaclab.sim import SimulationCfg  # 仿真配置
from isaaclab.terrains import TerrainImporterCfg  # 地形导入配置
from isaaclab.utils import configclass  # 配置类装饰器
from isaaclab.utils.math import subtract_frame_transforms  # 数学工具：坐标系变换

# ========================================
# 预定义配置导入
# ========================================
from isaaclab_assets import CRAZYFLIE_CFG  # Crazyflie四旋翼无人机配置 # isort: skip
from isaaclab.markers import CUBOID_MARKER_CFG  # 立方体标记配置 # isort: skip


# ========================================
# UI窗口管理类
# ========================================

class QuadcopterEnvWindow(BaseEnvWindow):
    """
    四旋翼无人机环境的窗口管理器

    这个类负责管理四旋翼无人机环境的用户界面窗口，包括：
    - 调试信息显示
    - 目标位置可视化控制
    - 环境参数实时监控

    继承自BaseEnvWindow，提供了标准的环境UI框架。
    """

    def __init__(self, env: QuadcopterEnv, window_name: str = "IsaacLab"):
        """
        初始化窗口管理器

        Args:
            env (QuadcopterEnv): 四旋翼环境实例，用于获取环境状态和控制参数
            window_name (str): 窗口名称，默认为"IsaacLab"
        """
        # 初始化基础窗口组件
        super().__init__(env, window_name)

        # 添加自定义UI元素到窗口
        with self.ui_window_elements["main_vstack"]:  # 主垂直布局容器
            with self.ui_window_elements["debug_frame"]:  # 调试信息框架
                with self.ui_window_elements["debug_vstack"]:  # 调试信息垂直布局
                    # 添加目标位置可视化控制元素
                    # 这将创建一个UI控件来切换目标位置的可视化显示
                    self._create_debug_vis_ui_element("targets", self.env)


# ========================================
# 环境配置类
# ========================================

@configclass
class QuadcopterEnvCfg(DirectRLEnvCfg):
    """
    四旋翼无人机环境配置类

    这个配置类定义了四旋翼无人机强化学习环境的所有参数，包括：
    - 环境基本参数（episode长度、动作/观测空间维度等）
    - 仿真参数（时间步长、物理材料属性等）
    - 机器人参数（推力重量比、力矩缩放等）
    - 奖励函数参数（各项奖励的权重系数）

    使用@configclass装饰器，支持配置的序列化和反序列化。
    """

    # ========================================
    # 环境基本参数
    # ========================================
    episode_length_s = 10.0        # 每个episode的持续时间（秒）
    decimation = 2                 # 动作重复次数，即每个动作执行2个仿真步
    action_space = 4               # 动作空间维度：[推力, roll力矩, pitch力矩, yaw力矩]
    observation_space = 12         # 观测空间维度：线速度(3) + 角速度(3) + 重力向量(3) + 目标位置(3)
    state_space = 0                # 状态空间维度（此环境中未使用额外状态）
    debug_vis = True               # 是否启用调试可视化

    # UI窗口类型，用于创建环境的用户界面
    ui_window_class_type = QuadcopterEnvWindow

    # ========================================
    # 仿真配置参数
    # ========================================
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 100,                # 仿真时间步长：0.01秒（100Hz）
        render_interval=decimation, # 渲染间隔，与动作重复次数一致
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",    # 摩擦力组合模式：相乘
            restitution_combine_mode="multiply", # 恢复系数组合模式：相乘
            static_friction=1.0,                # 静摩擦系数
            dynamic_friction=1.0,               # 动摩擦系数
            restitution=0.0,                    # 恢复系数（完全非弹性碰撞）
        ),
    )

    # ========================================
    # 地形配置参数
    # ========================================
    terrain = TerrainImporterCfg(
        prim_path="/World/ground",              # 地面在USD场景中的路径
        terrain_type="plane",                   # 地形类型：平面
        collision_group=-1,                     # 碰撞组ID（-1表示与所有组碰撞）
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",    # 地面摩擦力组合模式
            restitution_combine_mode="multiply", # 地面恢复系数组合模式
            static_friction=1.0,                # 地面静摩擦系数
            dynamic_friction=1.0,               # 地面动摩擦系数
            restitution=0.0,                    # 地面恢复系数
        ),
        debug_vis=False,                        # 不显示地形调试信息
    )

    # ========================================
    # 场景配置参数
    # ========================================
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=4096,          # 并行环境数量（支持大规模并行训练）
        env_spacing=2.5,        # 环境间距（米），防止不同环境中的无人机相互干扰
        replicate_physics=True  # 是否为每个环境复制物理场景（提高仿真精度）
    )

    # ========================================
    # 机器人配置参数
    # ========================================
    robot: ArticulationCfg = CRAZYFLIE_CFG.replace(
        prim_path="/World/envs/env_.*/Robot"    # 机器人在场景中的路径模式
    )
    thrust_to_weight = 1.9      # 推力重量比：1.9表示最大推力是重量的1.9倍
    moment_scale = 0.01         # 力矩缩放因子：将动作空间的力矩命令缩放到实际力矩

    # ========================================
    # 奖励函数权重参数
    # ========================================
    lin_vel_reward_scale = -0.05        # 线速度惩罚权重（负值表示惩罚高速度）
    ang_vel_reward_scale = -0.01        # 角速度惩罚权重（负值表示惩罚高角速度）
    distance_to_goal_reward_scale = 15.0 # 距离目标奖励权重（正值表示奖励接近目标）


# ========================================
# 主环境类
# ========================================

class QuadcopterEnv(DirectRLEnv):
    """
    四旋翼无人机强化学习环境主类

    这个类实现了完整的四旋翼无人机强化学习环境，包括：
    - 物理仿真管理
    - 动作空间处理（推力和力矩控制）
    - 观测空间构建（速度、姿态、目标位置等）
    - 奖励函数计算（距离、速度惩罚等）
    - 终止条件判断（碰撞、超时等）
    - 环境重置逻辑

    继承自DirectRLEnv，提供了高效的GPU并行仿真能力。
    """

    cfg: QuadcopterEnvCfg  # 环境配置实例

    def __init__(self, cfg: QuadcopterEnvCfg, render_mode: str | None = None, **kwargs):
        """
        初始化四旋翼无人机环境

        Args:
            cfg (QuadcopterEnvCfg): 环境配置对象，包含所有环境参数
            render_mode (str | None): 渲染模式，可选"human"、"rgb_array"等
            **kwargs: 其他传递给父类的参数
        """
        # 调用父类初始化方法，设置基础环境框架
        super().__init__(cfg, render_mode, **kwargs)

        # ========================================
        # 动作和力相关的张量初始化
        # ========================================

        # 存储当前动作命令 [推力, roll力矩, pitch力矩, yaw力矩]
        # 形状: (num_envs, 4)
        self._actions = torch.zeros(
            self.num_envs,
            gym.spaces.flatdim(self.single_action_space),
            device=self.device
        )

        # 应用到无人机机体的总推力向量 (世界坐标系)
        # 形状: (num_envs, 1, 3) - 1表示单个刚体，3表示xyz方向的力
        self._thrust = torch.zeros(self.num_envs, 1, 3, device=self.device)

        # 应用到无人机机体的总力矩向量 (机体坐标系)
        # 形状: (num_envs, 1, 3) - 1表示单个刚体，3表示xyz方向的力矩
        self._moment = torch.zeros(self.num_envs, 1, 3, device=self.device)

        # ========================================
        # 目标和任务相关变量
        # ========================================

        # 目标位置 (世界坐标系)
        # 形状: (num_envs, 3) - 每个环境都有独立的目标位置
        self._desired_pos_w = torch.zeros(self.num_envs, 3, device=self.device)

        # ========================================
        # 日志记录相关变量
        # ========================================

        # 用于记录每个episode中各项奖励的累积值
        self._episode_sums = {
            key: torch.zeros(self.num_envs, dtype=torch.float, device=self.device)
            for key in [
                "lin_vel",          # 线速度相关奖励累积
                "ang_vel",          # 角速度相关奖励累积
                "distance_to_goal", # 距离目标相关奖励累积
            ]
        }

        # ========================================
        # 机器人物理属性获取
        # ========================================

        # 获取无人机机体的body ID（用于施加外力和力矩）
        self._body_id = self._robot.find_bodies("body")[0]

        # 获取无人机的总质量（kg）
        self._robot_mass = self._robot.root_physx_view.get_masses()[0].sum()

        # 计算重力加速度的大小（m/s²）
        self._gravity_magnitude = torch.tensor(self.sim.cfg.gravity, device=self.device).norm()

        # 计算无人机的重量（N = kg * m/s²）
        self._robot_weight = (self._robot_mass * self._gravity_magnitude).item()

        # ========================================
        # 调试可视化设置
        # ========================================

        # 设置调试可视化（如果配置中启用的话）
        # 这将创建目标位置的可视化标记
        self.set_debug_vis(self.cfg.debug_vis)

    def _setup_scene(self):
        """
        设置仿真场景

        这个方法负责初始化整个仿真场景，包括：
        1. 创建和配置四旋翼无人机模型
        2. 设置地形环境
        3. 克隆多个并行环境
        4. 配置碰撞检测
        5. 添加光照效果

        该方法在环境初始化时被自动调用。
        """
        # ========================================
        # 1. 创建四旋翼无人机模型
        # ========================================

        # 基于配置创建关节机器人（四旋翼无人机）
        # Articulation类管理多关节机器人的物理仿真
        self._robot = Articulation(self.cfg.robot)

        # 将机器人添加到场景的关节机器人字典中
        # 键名"robot"用于后续引用这个机器人实例
        self.scene.articulations["robot"] = self._robot

        # ========================================
        # 2. 配置和创建地形
        # ========================================

        # 将场景配置传递给地形配置
        # 确保地形知道有多少个并行环境以及它们的间距
        self.cfg.terrain.num_envs = self.scene.cfg.num_envs      # 环境数量
        self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing # 环境间距

        # 根据配置创建地形实例
        # class_type是地形导入器的类，通常是TerrainImporter
        self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)

        # ========================================
        # 3. 克隆并复制环境
        # ========================================

        # 克隆环境以创建多个并行的仿真实例
        # copy_from_source=False 表示不从源环境复制，而是创建新的实例
        self.scene.clone_environments(copy_from_source=False)

        # ========================================
        # 4. 配置碰撞检测（CPU仿真特殊处理）
        # ========================================

        # 对于CPU仿真，需要显式过滤碰撞以提高性能
        # GPU仿真会自动处理碰撞优化
        if self.device == "cpu":
            self.scene.filter_collisions(global_prim_paths=[self.cfg.terrain.prim_path])

        # ========================================
        # 5. 添加场景光照
        # ========================================

        # 创建圆顶光照配置
        # 圆顶光提供均匀的环境光照，适合机器人仿真
        light_cfg = sim_utils.DomeLightCfg(
            intensity=2000.0,           # 光照强度
            color=(0.75, 0.75, 0.75)   # 光照颜色（RGB，略偏暖白）
        )

        # 在场景中创建光照
        # "/World/Light" 是光照在USD场景树中的路径
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor):
        """
        物理步进前的动作预处理

        这个方法在每个仿真步骤之前被调用，负责：
        1. 处理和限制输入动作
        2. 将归一化的动作转换为实际的物理量（推力和力矩）

        Args:
            actions (torch.Tensor): 来自策略网络的动作命令
                                  形状: (num_envs, 4)
                                  内容: [推力命令, roll力矩命令, pitch力矩命令, yaw力矩命令]
                                  范围: [-1, 1] (归一化)
        """
        # ========================================
        # 1. 动作预处理和限制
        # ========================================

        # 克隆动作张量并限制在[-1, 1]范围内
        # 这确保了动作不会超出预期范围，提高训练稳定性
        self._actions = actions.clone().clamp(-1.0, 1.0)

        # ========================================
        # 2. 推力计算和转换
        # ========================================

        # 将归一化的推力命令转换为实际推力（牛顿）
        # 推力公式解析：
        # - self._actions[:, 0]: 推力命令，范围[-1, 1]
        # - (self._actions[:, 0] + 1.0) / 2.0: 转换到[0, 1]范围
        # - self.cfg.thrust_to_weight: 推力重量比（1.9）
        # - self._robot_weight: 无人机重量（N）
        # 结果：推力范围从0到1.9倍重量
        self._thrust[:, 0, 2] = (
            self.cfg.thrust_to_weight *
            self._robot_weight *
            (self._actions[:, 0] + 1.0) / 2.0
        )

        # 注意：推力只在Z方向（向上），这符合四旋翼的物理特性
        # X和Y方向的推力保持为0，姿态控制通过力矩实现

        # ========================================
        # 3. 力矩计算和转换
        # ========================================

        # 将归一化的力矩命令转换为实际力矩（牛顿·米）
        # - self._actions[:, 1:]: 力矩命令 [roll, pitch, yaw]，范围[-1, 1]
        # - self.cfg.moment_scale: 力矩缩放因子（0.01）
        # 结果：力矩范围为[-0.01, 0.01] N·m
        self._moment[:, 0, :] = self.cfg.moment_scale * self._actions[:, 1:]

    def _apply_action(self):
        """
        应用动作到物理仿真

        这个方法将预处理后的推力和力矩应用到无人机的物理模型上。
        Isaac Sim会在下一个物理步骤中计算这些力和力矩对无人机运动的影响。
        """
        # 将计算好的推力和力矩施加到无人机机体上
        # - self._thrust: 推力向量 (num_envs, 1, 3)
        # - self._moment: 力矩向量 (num_envs, 1, 3)
        # - body_ids=self._body_id: 指定施加力的目标刚体（无人机机体）
        self._robot.set_external_force_and_torque(
            self._thrust,
            self._moment,
            body_ids=self._body_id
        )

    def _get_observations(self) -> dict:
        """
        构建环境观测

        这个方法构建强化学习智能体的观测向量，包含无人机的状态信息
        和任务相关信息。观测空间设计对训练效果至关重要。

        Returns:
            dict: 包含观测数据的字典
                - "policy": 策略网络的观测向量 (num_envs, 12)
        """
        # ========================================
        # 1. 计算目标位置在机体坐标系中的表示
        # ========================================

        # 将世界坐标系中的目标位置转换到机体坐标系
        # 这样智能体可以知道目标相对于自身的位置和方向
        # subtract_frame_transforms 执行坐标系变换：
        # desired_pos_b = R^T * (desired_pos_w - robot_pos_w)
        # 其中 R 是从机体到世界的旋转矩阵
        desired_pos_b, _ = subtract_frame_transforms(
            self._robot.data.root_pos_w,    # 无人机当前世界位置
            self._robot.data.root_quat_w,   # 无人机当前世界姿态（四元数）
            self._desired_pos_w             # 目标世界位置
        )

        # ========================================
        # 2. 构建完整观测向量
        # ========================================

        # 将所有观测分量连接成一个向量
        obs = torch.cat([
            # 线速度（机体坐标系）- 3维
            # 告诉智能体当前的运动状态
            self._robot.data.root_lin_vel_b,

            # 角速度（机体坐标系）- 3维
            # 告诉智能体当前的旋转状态
            self._robot.data.root_ang_vel_b,

            # 重力向量在机体坐标系中的投影 - 3维
            # 告诉智能体当前的姿态（相对于重力方向）
            self._robot.data.projected_gravity_b,

            # 目标位置（机体坐标系）- 3维
            # 告诉智能体目标的相对位置
            desired_pos_b,
        ], dim=-1)  # 在最后一个维度上连接，结果形状: (num_envs, 12)

        # ========================================
        # 3. 返回观测字典
        # ========================================

        # Isaac Lab要求观测以字典形式返回
        # "policy"键对应策略网络的输入
        observations = {"policy": obs}
        return observations

    def _get_rewards(self) -> torch.Tensor:
        """
        计算奖励函数

        这个方法实现了多目标奖励函数，平衡以下几个方面：
        1. 鼓励接近目标位置
        2. 惩罚过高的线速度（鼓励平稳飞行）
        3. 惩罚过高的角速度（鼓励稳定姿态）

        Returns:
            torch.Tensor: 每个环境的总奖励值 (num_envs,)
        """
        # ========================================
        # 1. 计算各项奖励分量
        # ========================================

        # 线速度惩罚：速度越高惩罚越大
        # 计算线速度的平方和，鼓励平稳飞行
        lin_vel = torch.sum(torch.square(self._robot.data.root_lin_vel_b), dim=1)

        # 角速度惩罚：角速度越高惩罚越大
        # 计算角速度的平方和，鼓励稳定姿态
        ang_vel = torch.sum(torch.square(self._robot.data.root_ang_vel_b), dim=1)

        # 距离目标的欧几里得距离
        distance_to_goal = torch.linalg.norm(
            self._desired_pos_w - self._robot.data.root_pos_w, dim=1
        )

        # 将距离映射到[0, 1]范围的奖励
        # 使用tanh函数进行平滑映射：
        # - 距离为0时，奖励为1（最大奖励）
        # - 距离增大时，奖励逐渐减小
        # - 0.8是缩放参数，控制奖励衰减的速度
        distance_to_goal_mapped = 1 - torch.tanh(distance_to_goal / 0.8)

        # ========================================
        # 2. 应用权重和时间缩放
        # ========================================

        # 构建奖励字典，每项都乘以对应的权重和时间步长
        rewards = {
            # 线速度惩罚（负权重）
            "lin_vel": lin_vel * self.cfg.lin_vel_reward_scale * self.step_dt,

            # 角速度惩罚（负权重）
            "ang_vel": ang_vel * self.cfg.ang_vel_reward_scale * self.step_dt,

            # 距离目标奖励（正权重）
            "distance_to_goal": distance_to_goal_mapped * self.cfg.distance_to_goal_reward_scale * self.step_dt,
        }

        # ========================================
        # 3. 计算总奖励并记录日志
        # ========================================

        # 将所有奖励分量相加得到总奖励
        reward = torch.sum(torch.stack(list(rewards.values())), dim=0)

        # 记录每个episode的奖励累积值（用于日志和分析）
        for key, value in rewards.items():
            self._episode_sums[key] += value

        return reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """
        判断环境终止条件

        这个方法定义了两种类型的episode终止条件：
        1. 异常终止（died）：无人机坠毁或飞得太高
        2. 正常终止（time_out）：达到最大episode长度

        Returns:
            tuple[torch.Tensor, torch.Tensor]: (died, time_out)
                - died: 异常终止标志 (num_envs,) - 布尔张量
                - time_out: 超时终止标志 (num_envs,) - 布尔张量
        """
        # ========================================
        # 1. 超时终止条件
        # ========================================

        # 检查是否达到最大episode长度
        # episode_length_buf 记录当前episode已经运行的步数
        # max_episode_length 是配置中定义的最大步数
        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # ========================================
        # 2. 异常终止条件
        # ========================================

        # 检查无人机是否处于异常状态：
        # - 高度过低（< 0.1m）：可能坠毁到地面
        # - 高度过高（> 2.0m）：可能失控飞走
        # 使用逻辑或操作，满足任一条件就认为异常终止
        died = torch.logical_or(
            self._robot.data.root_pos_w[:, 2] < 0.1,   # 高度过低
            self._robot.data.root_pos_w[:, 2] > 2.0    # 高度过高
        )

        return died, time_out

    def _reset_idx(self, env_ids: torch.Tensor | None):
        """
        重置指定的环境

        这个方法负责重置需要重新开始的环境，包括：
        1. 记录和清理episode统计数据
        2. 重置机器人状态到初始位置
        3. 生成新的随机目标位置
        4. 重置所有相关变量

        Args:
            env_ids (torch.Tensor | None): 需要重置的环境ID列表
                                         如果为None，则重置所有环境
        """
        # ========================================
        # 1. 处理环境ID参数
        # ========================================

        # 如果没有指定环境ID或者要重置所有环境，则使用全部环境
        if env_ids is None or len(env_ids) == self.num_envs:
            env_ids = self._robot._ALL_INDICES

        # ========================================
        # 2. 记录episode结束时的统计数据
        # ========================================

        # 计算episode结束时到目标的最终距离（用于性能评估）
        final_distance_to_goal = torch.linalg.norm(
            self._desired_pos_w[env_ids] - self._robot.data.root_pos_w[env_ids], dim=1
        ).mean()

        # 记录奖励相关的统计数据
        extras = dict()
        for key in self._episode_sums.keys():
            # 计算每个奖励分量的平均值
            episodic_sum_avg = torch.mean(self._episode_sums[key][env_ids])
            # 归一化到每秒的奖励（除以episode长度）
            extras["Episode_Reward/" + key] = episodic_sum_avg / self.max_episode_length_s
            # 清零累积奖励，为下一个episode做准备
            self._episode_sums[key][env_ids] = 0.0

        # 初始化日志字典
        self.extras["log"] = dict()
        self.extras["log"].update(extras)

        # 记录终止原因统计
        extras = dict()
        # 统计因异常而终止的环境数量
        extras["Episode_Termination/died"] = torch.count_nonzero(self.reset_terminated[env_ids]).item()
        # 统计因超时而终止的环境数量
        extras["Episode_Termination/time_out"] = torch.count_nonzero(self.reset_time_outs[env_ids]).item()
        # 记录最终距离目标的距离
        extras["Metrics/final_distance_to_goal"] = final_distance_to_goal.item()
        self.extras["log"].update(extras)

        # ========================================
        # 3. 重置机器人和环境状态
        # ========================================

        # 重置机器人的物理状态（位置、速度、关节状态等）
        self._robot.reset(env_ids)

        # 调用父类的重置方法，处理基础环境重置逻辑
        super()._reset_idx(env_ids)

        # ========================================
        # 4. 随机化episode长度（训练优化）
        # ========================================

        # 如果重置所有环境，随机化episode长度
        # 这避免了所有环境同时重置造成的训练不稳定
        if len(env_ids) == self.num_envs:
            self.episode_length_buf = torch.randint_like(
                self.episode_length_buf,
                high=int(self.max_episode_length)
            )

        # ========================================
        # 5. 重置动作和生成新目标
        # ========================================

        # 清零动作命令
        self._actions[env_ids] = 0.0

        # 生成新的随机目标位置
        # X和Y坐标：在[-2, 2]米范围内随机
        self._desired_pos_w[env_ids, :2] = torch.zeros_like(
            self._desired_pos_w[env_ids, :2]
        ).uniform_(-2.0, 2.0)

        # 将目标位置偏移到对应环境的原点
        # 这确保每个并行环境的目标都在其自己的空间内
        self._desired_pos_w[env_ids, :2] += self._terrain.env_origins[env_ids, :2]

        # Z坐标（高度）：在[0.5, 1.5]米范围内随机
        # 这确保目标在合理的飞行高度范围内
        self._desired_pos_w[env_ids, 2] = torch.zeros_like(
            self._desired_pos_w[env_ids, 2]
        ).uniform_(0.5, 1.5)

        # ========================================
        # 6. 重置机器人物理状态到初始配置
        # ========================================

        # 获取默认关节位置和速度
        joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_vel = self._robot.data.default_joint_vel[env_ids]

        # 获取默认根节点状态（位置、姿态、速度）
        default_root_state = self._robot.data.default_root_state[env_ids]

        # 将机器人位置偏移到对应环境的原点
        default_root_state[:, :3] += self._terrain.env_origins[env_ids]

        # 写入新的根节点姿态到仿真（位置和四元数）
        self._robot.write_root_pose_to_sim(default_root_state[:, :7], env_ids)

        # 写入新的根节点速度到仿真（线速度和角速度）
        self._robot.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)

        # 写入新的关节状态到仿真
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

    def _set_debug_vis_impl(self, debug_vis: bool):
        """
        设置调试可视化的具体实现

        这个方法控制调试可视化元素的创建和显示，主要用于：
        1. 显示目标位置标记
        2. 帮助开发者和研究者理解环境状态
        3. 调试训练过程中的问题

        Args:
            debug_vis (bool): 是否启用调试可视化
        """
        # ========================================
        # 启用调试可视化
        # ========================================
        if debug_vis:
            # 如果还没有创建目标位置可视化器，则创建一个
            if not hasattr(self, "goal_pos_visualizer"):
                # 复制默认的立方体标记配置
                marker_cfg = CUBOID_MARKER_CFG.copy()

                # 设置标记的大小（5cm x 5cm x 5cm的小立方体）
                marker_cfg.markers["cuboid"].size = (0.05, 0.05, 0.05)

                # 设置标记在USD场景中的路径
                # 这个路径决定了标记在场景树中的位置
                marker_cfg.prim_path = "/Visuals/Command/goal_position"

                # 创建可视化标记实例
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)

            # 设置可视化标记为可见
            self.goal_pos_visualizer.set_visibility(True)

        # ========================================
        # 禁用调试可视化
        # ========================================
        else:
            # 如果可视化器存在，则隐藏它
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)

    def _debug_vis_callback(self, event):
        """
        调试可视化回调函数

        这个方法在每个仿真步骤中被调用（如果启用了调试可视化），
        负责更新可视化标记的位置和状态。

        Args:
            event: 仿真事件对象（Isaac Sim传递的事件信息）
                  注意：这个参数在当前实现中未使用，但保留以符合回调接口
        """
        # 更新目标位置标记的显示位置
        # self._desired_pos_w 包含所有环境的目标位置
        # 可视化器会在每个目标位置显示一个小立方体
        self.goal_pos_visualizer.visualize(self._desired_pos_w)


# ========================================
# 文件结束标记
# ========================================

"""
总结：四旋翼无人机强化学习环境

这个文件实现了一个完整的四旋翼无人机强化学习环境，具有以下特点：

🎯 任务设计：
- 目标：控制四旋翼无人机飞到随机生成的目标位置
- 动作空间：4维连续动作（推力 + 3轴力矩）
- 观测空间：12维状态（速度、姿态、目标位置）

🏗️ 架构特点：
- 基于Isaac Lab框架，支持GPU加速仿真
- 支持大规模并行训练（最多4096个环境）
- 模块化设计，易于扩展和修改

🎮 控制方式：
- 推力控制：垂直升降
- 力矩控制：姿态调整（roll, pitch, yaw）
- 动作范围：[-1, 1]归一化

🏆 奖励设计：
- 距离奖励：鼓励接近目标
- 速度惩罚：鼓励平稳飞行
- 姿态惩罚：鼓励稳定控制

🔧 技术实现：
- 高效的张量操作（PyTorch）
- 实时物理仿真（Isaac Sim）
- 可视化调试工具
- 完整的日志记录系统

这个环境可以用于训练各种强化学习算法，如PPO、SAC、TD3等，
是研究无人机控制和多智能体系统的理想平台。
"""