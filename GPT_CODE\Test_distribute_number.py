# Re-run: Implementing Section 3.3 model + A & B (self-contained).
import numpy as np
import matplotlib.pyplot as plt
import json
from math import pi

# --- Paper parameters (Table 1 and Sec.3.3) ---
wave_params = {
    'omega_x': [1.0, 10.0],   # rad/s
    'A_x':     [0.1, 0.03],   # m
    'phi_x':   [0.0, 0.0],    # rad
    'omega_y': [2.0, 7.0],    # rad/s
    'A_y':     [0.07, 0.04],
    'phi_y':   [0.0, 0.0]
}

# cylinder geometry (paper)
R = 0.04   # m (radius)
H = 0.3    # m (height)
V = pi*R**2*H

# Morrison coefficients (paper uses CM=0.8, CD=0.8)
CM = 0.8
CD = 0.8
rho_water = 1025.0
rho_air = 1.29

# computational domain for propeller density (paper uses prop diameter D_prop and prop radius Rp)
D_prop = 15.0 * 0.0254   # 15 inch -> meters (prop diameter)
Rp = D_prop / 2.0        # prop radius
H_dom = Rp               # paper: domain height = Rp

# Helper: linear VOF density model per Eq.(25)
def rho_eff_vof(D_rel):
    # D_rel is relative wave height D (paper uses D in [-1, inf) sense)
    # per Eq.(25): D>0 -> rho_air; -1 < D <= 0 -> linear interpolation; D <= -1 -> rho_water
    if D_rel > 0:
        return rho_air
    elif D_rel <= -1:
        return rho_water
    else:
        return (rho_water - rho_air) * D_rel + rho_air

# Wave k from deep water dispersion: omega^2 = g k  => k = omega^2 / g
g = 9.81
def k_from_omega(omega):
    return omega**2 / g

# Velocity components from potential at given (x,y,z,t) for each harmonic term
def wave_velocity_components(x,y,z,t):
    ux_total = 0.0
    uy_total = 0.0
    udot_x_total = 0.0
    udot_y_total = 0.0
    # x-direction waves
    for A,omega,phi in zip(wave_params['A_x'], wave_params['omega_x'], wave_params['phi_x']):
        k = k_from_omega(omega)
        exp_kz = np.exp(k*z)  # z<=0
        phase = k*x - omega*t + phi
        ux = A*omega*exp_kz*np.cos(phase)       # u_x (from derivation)
        udotx = -A*omega**2*exp_kz*np.sin(phase)
        ux_total += ux
        udot_x_total += udotx
    # y-direction waves
    for A,omega,phi in zip(wave_params['A_y'], wave_params['omega_y'], wave_params['phi_y']):
        k = k_from_omega(omega)
        exp_kz = np.exp(k*z)
        phase = k*y - omega*t + phi
        uy = A*omega*exp_kz*np.cos(phase)
        udoty = -A*omega**2*exp_kz*np.sin(phase)
        uy_total += uy
        udot_y_total += udoty
    return ux_total, uy_total, udot_x_total, udot_y_total

# Morrison micro-element forces for horizontal direction: per Eq.(18)
def morrison_integral_at_point(x,y,z_center,t):
    z_top = z_center + H/2.0
    z_bot = z_center - H/2.0
    # immersion interval where z_seg <= 0 (water is z<=0)
    z_u = min(z_top, 0.0)
    z_l = z_bot
    if z_u <= z_l:
        # no immersion
        return 0.0, 0.0
    # integrate using simple numeric integration on nz points
    nz = 100
    zs = np.linspace(z_l, z_u, nz)
    dx = zs[1]-zs[0] if nz>1 else 0.0
    Fx = 0.0
    Fy = 0.0
    for zi in zs:
        ux, uy, udotx, udoty = wave_velocity_components(x,y,zi,t)
        dFx = rho_water*CM*pi*R**2 * udotx + rho_water*CD*R * ux*abs(ux)
        dFy = rho_water*CM*pi*R**2 * udoty + rho_water*CD*R * uy*abs(uy)
        Fx += dFx*dx
        Fy += dFy*dx
    return Fx, Fy

# A: function to compute wave force & moment on vehicle at given pose and time
def compute_wave_force_moment(x,y,z,phi,theta,psi,t):
    Fx, Fy = morrison_integral_at_point(x,y,z,t)
    # assemble into vector in earth frame and map to body frame using rotation R1^T (body<-earth)
    cphi = np.cos(phi); sphi = np.sin(phi)
    ctheta = np.cos(theta); stheta = np.sin(theta)
    cpsi = np.cos(psi); spsi = np.sin(psi)
    R1 = np.array([
        [ctheta*cpsi, sphi*stheta*cpsi - cphi*spsi, cphi*stheta*cpsi + sphi*spsi],
        [ctheta*spsi, sphi*stheta*spsi + cphi*cpsi, cphi*stheta*spsi - sphi*cpsi],
        [-stheta,     sphi*ctheta,                 cphi*ctheta]
    ])
    F_earth = np.array([Fx, Fy, 0.0])
    F_body = R1.T.dot(F_earth)
    rB = np.zeros(3)   # paper's simplified assumption for many cases
    M_body = np.cross(rB, F_body)
    return F_body, M_body

# B: plot Fwave,x(t) at x=y=0 for several depths (z values)
tvec = np.linspace(0, 10, 800)  # 10 seconds
depths = [-1.5, -0.5, -0.087, -0.02]  # z positions (m); negative = below water surface

plt.figure(figsize=(10,6))
for z in depths:
    Fx_t = []
    for tt in tvec:
        Fx, Fy = morrison_integral_at_point(0.0, 0.0, z, tt)
        Fx_t.append(Fx)
    plt.plot(tvec, Fx_t, label=f"z={z:.3f} m")
plt.xlabel("t (s)")
plt.ylabel("F_wave,x (N)")
plt.title("Wave force F_wave,x(t) at x=y=0 for different depths (Morrison integration)")
plt.legend()
plt.grid(True)
plt.show()

