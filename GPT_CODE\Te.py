
# -*- coding: utf-8 -*-
"""
HAUV关键方程、参数查表与仿真脚本骨架
- 面向三篇论文：
  1) Ocean Engineering 2022: "Dynamics and control of hybrid aerial underwater vehicle subject to disturbances"（简称 OE-2022）
  2) IEEE JOE 2022: "Takeoff and Landing Control of a Hybrid Aerial Underwater Vehicle on Disturbed Water’s Surface"（简称 JOE-2022）
  3) Surfing Algorithm: "Agile and Safe Transition Strategy for HAUV in Waves"（简称 Surfing-2023）

本脚本提供：
- 从论文中“已抽取/整理”的关键方程接口（按论文符号命名）与参数对照表
- 查表函数：螺旋桨推力/功率曲线插值（支持 air/water 两套曲线与界面混合插值）
- 控制器骨架：二阶SMC（OE-2022）与DSC+NDO（JOE-2022）框架
- 6DOF动力学骨架：带附加质量/附加惯量、二次阻尼（Surfing-2023样式）
- 任务/海况：提供水出（OE-2022的式(56)）与简化Airy波/风扰动接口
- 运行入口：给出最小可运行示例（无需外部数据即可跑通，但强烈建议用真实桨曲线CSV替换）

重要说明：
- 图中曲线数据（如 OE-2022 Fig.3 的桨曲线）需通过外部数字化工具（如 WebPlotDigitizer）导出CSV；本脚本提供 load_curve_csv 与线性插值器。
- 文中某些符号/系数（如 ks、V、界面损失系数K_M/K_J/D）需结合平台实测或论文全文细节二次确认，本脚本给出可配置占位与合理默认值。
- 稳定性证明与完整控制律推导请参考原文；此处实现工程可用的“骨架版本”，便于替换/扩展。

建议依赖：
- 仅使用 numpy，避免SciPy依赖；插值采用自编线性插值器。

作者：HAUV文献自动对照/仿真骨架
"""

from __future__ import annotations
from dataclasses import dataclass, field
from typing import Callable, Dict, Optional, Tuple, List
import numpy as np
import csv
import math
import os


# =========================
# 1) 工具：线性插值与CSV加载
# =========================

def lininterp1(xq: np.ndarray, x: np.ndarray, y: np.ndarray, left=None, right=None) -> np.ndarray:
    """单调x的一维线性插值（向量化），不依赖SciPy。
    - x: 已排序的自变量 (N,)
    - y: 对应的因变量 (N,)
    - xq: 待插值点 (M,)
    - left/right: 外推时的返回值（默认截断为边界值）
    """
    x = np.asarray(x, dtype=float)
    y = np.asarray(y, dtype=float)
    xq = np.asarray(xq, dtype=float)

    if x.ndim != 1 or y.ndim != 1 or x.shape[0] != y.shape[0]:
        raise ValueError("x,y需为同长度一维数组")
    if not np.all(np.diff(x) >= 0):
        raise ValueError("x需非降序（建议严格递增）")

    if left is None:
        left = y[0]
    if right is None:
        right = y[-1]

    yi = np.empty_like(xq, dtype=float)
    # 对外侧截断
    yi[xq <= x[0]] = left
    yi[xq >= x[-1]] = right

    # 内部点
    mask = (xq > x[0]) & (xq < x[-1])
    xqi = xq[mask]
    # 查找区间索引
    idx = np.searchsorted(x, xqi) - 1
    idx = np.clip(idx, 0, len(x) - 2)
    x0 = x[idx]
    x1 = x[idx + 1]
    y0 = y[idx]
    y1 = y[idx + 1]
    t = (xqi - x0) / np.maximum(x1 - x0, 1e-12)
    yi[mask] = y0 + t * (y1 - y0)
    return yi


def load_curve_csv(path: str) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
    """加载CSV曲线，要求第一列为自变量（如 rpm），后续列为命名列。
    CSV格式示例:
        rpm,thrust_N,power_W
        0,0,0
        1000,1.2,30
        ...
    返回:
        x: (N,)
        columns: dict{name: (N,)}
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"找不到CSV: {path}")
    with open(path, "r", newline="") as f:
        reader = csv.reader(f)
        rows = list(reader)
    header = [h.strip() for h in rows[0]]
    data = np.array([[float(v) for v in r] for r in rows[1:]], dtype=float)
    x = data[:, 0]
    columns = {}
    for j, name in enumerate(header[1:], start=1):
        columns[name] = data[:, j]
    return x, columns


# =========================
# 2) 查表：桨曲线与界面混合
# =========================

@dataclass
class PropCurve:
    medium: str  # 'air' or 'water'
    rpm_grid: np.ndarray
    thrust_N: np.ndarray
    power_W: Optional[np.ndarray] = None

    def thrust(self, rpm: np.ndarray) -> np.ndarray:
        return lininterp1(rpm, self.rpm_grid, self.thrust_N, left=0.0, right=self.thrust_N[-1])

    def power(self, rpm: np.ndarray) -> np.ndarray:
        if self.power_W is None:
            # 简单近似：P ~ T * (rpm比例)，占位
            base = self.thrust(rpm)
            return base * np.maximum(rpm, 0.0) / max(self.rpm_grid[-1], 1.0)
        return lininterp1(rpm, self.rpm_grid, self.power_W, left=0.0, right=self.power_W[-1])


@dataclass
class InterfaceBlend:
    """界面混合插值：基于浸没系数D进行air/water插值
    - D定义参考OE-2022（图示范围：D in [-0.5, 0]），D<-0.5近似全水下，D>0近似全空气
    """
    air_curve: PropCurve
    water_curve: PropCurve

    def alpha_air(self, D: float) -> float:
        # 线性映射：D<=-0.5 -> 0（全水），D>=0 -> 1（全空气）
        return float(np.clip((D + 0.5) / 0.5, 0.0, 1.0))

    def blended_thrust(self, rpm: np.ndarray, D: float) -> np.ndarray:
        a = self.alpha_air(D)
        return a * self.air_curve.thrust(rpm) + (1 - a) * self.water_curve.thrust(rpm)

    def blended_power(self, rpm: np.ndarray, D: float) -> np.ndarray:
        a = self.alpha_air(D)
        return a * self.air_curve.power(rpm) + (1 - a) * self.water_curve.power(rpm)


def default_prop_curves(max_rpm_air: float = 6243.0, max_thrust_air: float = 49.5) -> Tuple[PropCurve, PropCurve]:
    """无CSV时的占位桨曲线（建议尽快替换为实测/文献CSV）
    - 近似设定：空气中T ~ (rpm/max)^2 * max_thrust_air
    - 水中给更大系数（例如2.0倍），仅为演示占位
    """
    rpm_grid = np.linspace(0.0, max_rpm_air, 21)
    T_air = (rpm_grid / max_rpm_air) ** 2 * max_thrust_air
    # 占位：水中同桨效率常低，但密度大，具体取决于通气/空化/导管等；此处仅给演示曲线
    water_factor = 1.5
    T_water = (rpm_grid / max_rpm_air) ** 2 * max_thrust_air * water_factor

    air = PropCurve("air", rpm_grid, T_air, power_W=None)
    water = PropCurve("water", rpm_grid, T_water, power_W=None)
    return air, water


# =========================
# 3) 物理参数与符号对照
# =========================

@dataclass
class VehicleParams:
    # 质量与惯量
    m: float = 2.0                   # 质量 [kg] (OE-2022 Table 3)
    Ixx: float = 0.032               # 惯量 [kg m^2]
    Iyy: float = 0.032
    Izz: float = 0.026
    # 尺寸
    H: float = 0.3                   # 高 [m]
    R: float = 0.04                  # 半径 [m]
    L: float = 0.4                   # 臂长 [m]
    # 介质密度
    rho_air: float = 1.29
    rho_water: float = 1025.0
    g: float = 9.81
    # 水动力二次阻尼（Surfing-2023定义风格）
    Xu_abs: float = 154.0            # |Xu|，实际阻力项为 -Xu|u| u/|u| = -Xu u |u|
    Yv_abs: float = 154.0
    Zw_abs: float = 129.0
    Kp_abs: float = 10.0             # 角阻尼
    Mq_abs: float = 10.0
    Nr_abs: float = 15.0
    # 附加质量/惯量（OE-2022 Table 3）
    Xudot: float = -1.39             # 注意原表为负，常以 M_eff = m - kxx * Xudot 形式使用
    Yvdot: float = -1.39
    Zwdot: float = -0.94
    Kpdot: float = -0.027
    Mqdot: float = -0.027
    Nrdot: float = -0.013
    # 浮力/体积/界面损失
    V_hull: float = 0.002            # 排水体积 [m^3]（占位，按平台测量修正）
    ks: float = 1.0                  # “损失/浸没系数”缩放（OE-2022中ks出现在浮力/阻尼等）
    # 混合/切换参数
    K_M: float = 1.0                 # (M0 - K_M M_a) 中的系数（占位）
    K_J: float = 1.0                 # (J0 - K_J J_a)
    # 推进/混控
    motor_tau_coeff: float = 0.02    # 桨反扭矩系数（占位）
    rpm_max: float = 6243.0
    thrust_max_per_motor: float = 49.5

    def inertia_matrix(self) -> np.ndarray:
        return np.diag([self.Ixx, self.Iyy, self.Izz])

    def added_mass_vec(self) -> np.ndarray:
        return np.array([self.Xudot, self.Yvdot, self.Zwdot], dtype=float)

    def added_inertia_vec(self) -> np.ndarray:
        return np.array([self.Kpdot, self.Mqdot, self.Nrdot], dtype=float)


def symbol_cross_reference() -> Dict[str, Dict[str, str]]:
    """三文符号/通道/控制量对照（用于阅读/标注）
    - 注意：式号因版本不同或排版略有偏差，以目标论文为准
    """
    return {
        "总推力": {"OE-2022": "uf", "JOE-2022": "U1", "Surfing-2023": "Fair·1 + Fwater·1 (合力)"},
        "力矩向量": {"OE-2022": "Mc (或 τp,τq,τr)", "JOE-2022": "(U2,U3,U4)", "Surfing-2023": "Mair + Mwater"},
        "动力学平动": {"OE-2022": "方程(12)", "JOE-2022": "式(36)-(39)的并列6DoF", "Surfing-2023": "6DoF + 二次阻尼DM"},
        "动力学转动": {"OE-2022": "方程(13)", "JOE-2022": "式(36)-(39)的并列6DoF", "Surfing-2023": "6DoF + 二次角阻尼DJ"},
        "位置环-DSC": {"JOE-2022": "式(40)-(45)+(42)滤波+(59)NDO"},
        "姿态环-DSC": {"JOE-2022": "式(50),(53),(58)+(61)NDO"},
        "高度环-简化": {"OE-2022": "式(49)"},
        "水出轨迹": {"OE-2022": "式(56)"},
        "SMC李雅普诺夫": {"OE-2022": "式(45)-(47)附近"},
        "NDO稳定性": {"JOE-2022": "式(86)-(88)与定理1"},
        "波模型": {"Surfing-2023": "Airy波 + Froude–Krylov假设"}
    }


# =========================
# 4) 动态模型（6DoF骨架）
# =========================

@dataclass
class EnvState:
    wind_body: np.ndarray = field(default_factory=lambda: np.zeros(3))  # 机体系风速 [u_w,v_w,w_w]
    wave_elev: float = 0.0                                             # 波面抬升 ζ
    wave_accel_world: np.ndarray = field(default_factory=lambda: np.zeros(3))  # 波致加速度（FK近似）


def euler_to_R(phi: float, theta: float, psi: float) -> np.ndarray:
    """Z-Y-X 欧拉角到方向余弦矩阵 R^b_n (世界->机体)"""
    c, s = np.cos, np.sin
    Rz = np.array([[c(psi), -s(psi), 0],
                   [s(psi),  c(psi), 0],
                   [0,       0,      1]])
    Ry = np.array([[ c(theta), 0, s(theta)],
                   [ 0,        1, 0],
                   [-s(theta), 0, c(theta)]])
    Rx = np.array([[1, 0,       0],
                   [0, c(phi), -s(phi)],
                   [0, s(phi),  c(phi)]])
    return Rx @ Ry @ Rz


def euler_kinematics_matrix(phi: float, theta: float) -> np.ndarray:
    """欧拉角速率到角速度的映射逆矩阵 T(φ,θ)： [p,q,r]^T = T * [φdot, θdot, ψdot]^T"""
    c, s = np.cos, np.sin
    if abs(np.cos(theta)) < 1e-6:
        theta = float(np.clip(theta, -np.pi/2+1e-6, np.pi/2-1e-6))
    T = np.array([
        [1, 0, -s(theta)],
        [0, c(phi), s(phi)*c(theta)],
        [0, -s(phi), c(phi)*c(theta)]
    ])
    return T


def quad_mixing_matrix(L: float, b_tau: float) -> np.ndarray:
    """Quad-X混控矩阵 B: [U1,U2,U3,U4]^T = B * [T1,T2,T3,T4]^T
    - U1: 总推力（沿 -zb，向上为正）
    - U2: 翻滚力矩（x轴），U3: 俯仰力矩（y轴），U4: 偏航力矩（z轴）
    - b_tau: 桨反扭矩系数（N->N*m）
    """
    # X布局（旋翼1-4：前左、前右、后右、后左），需与实际一致
    return np.array([
        [ 1,  1,  1,  1],
        [ 0,  L,  0, -L],
        [-L,  0,  L,  0],
        [ b_tau, -b_tau, b_tau, -b_tau]
    ], dtype=float)


def quad_mixing_inverse(B: np.ndarray) -> np.ndarray:
    """逆混控： [T1..T4]^T = B_pinv * [U1..U4]^T（最小二乘）"""
    return np.linalg.pinv(B)


@dataclass
class MotorSet:
    """四桨推进模型：给定各桨rpm与浸没系数D_i，返回推力/功率"""
    blend: InterfaceBlend
    rpm_limits: Tuple[float, float] = (0.0, 6243.0)

    def thrusts(self, rpm4: np.ndarray, D4: np.ndarray) -> np.ndarray:
        """返回四桨推力[N]"""
        T = np.zeros(4, dtype=float)
        for i in range(4):
            rpi = np.clip(rpm4[i], self.rpm_limits[0], self.rpm_limits[1])
            T[i] = float(self.blend.blended_thrust(np.array([rpi]), float(D4[i]))[0])
        return T

    def powers(self, rpm4: np.ndarray, D4: np.ndarray) -> np.ndarray:
        P = np.zeros(4, dtype=float)
        for i in range(4):
            rpi = np.clip(rpm4[i], self.rpm_limits[0], self.rpm_limits[1])
            P[i] = float(self.blend.blended_power(np.array([rpi]), float(D4[i]))[0])
        return P


@dataclass
class HAUVState:
    """状态向量（世界坐标为n，机体为b）
    - η = [x,y,z, φ,θ,ψ]
    - ν = [u,v,w, p,q,r]
    """
    eta: np.ndarray = field(default_factory=lambda: np.zeros(6))
    nu: np.ndarray = field(default_factory=lambda: np.zeros(6))

    def copy(self) -> "HAUVState":
        return HAUVState(self.eta.copy(), self.nu.copy())


@dataclass
class Disturbance:
    F_env_b: np.ndarray = field(default_factory=lambda: np.zeros(3))
    M_env_b: np.ndarray = field(default_factory=lambda: np.zeros(3))


def hydrodynamic_quadratic_damping(nu_b: np.ndarray, P: VehicleParams) -> Tuple[np.ndarray, np.ndarray]:
    """二次阻尼（Surfing-2023: DM, DJ）
    Fd = -diag([Xu|u|, Yv|v|, Zw|w|]) * sign(v) 近似实现为 -coeff * v * |v|
    Md = -diag([Kp|p|, Mq|q|, Nr|r|]) * ω_unit
    """
    u, v, w, p, q, r = nu_b
    Fd = -np.array([
        P.Xu_abs * u * abs(u),
        P.Yv_abs * v * abs(v),
        P.Zw_abs * w * abs(w)
    ], dtype=float)
    Md = -np.array([
        P.Kp_abs * p * abs(p),
        P.Mq_abs * q * abs(q),
        P.Nr_abs * r * abs(r)
    ], dtype=float)
    return Fd, Md


def buoyancy_force(P: VehicleParams, immersion_frac: float) -> float:
    """浮力近似：B = ks * rho_water * g * V_sub
    - immersion_frac: 0..1（0全出水，1全浸没）占位（实际应由几何/姿态/波面计算）
    """
    V_sub = np.clip(immersion_frac, 0.0, 1.0) * P.V_hull
    return P.ks * P.rho_water * P.g * V_sub


def mass_matrices(P: VehicleParams, immersion_frac: float) -> Tuple[np.ndarray, np.ndarray]:
    """(M_trans, J_rot) 有效质量与惯量（OE-2022: (M0 - K_M M_a), (J0 - K_J J_a)）
    - 采用对角化近似
    """
    Ma = -np.array([P.Xudot, P.Yvdot, P.Zwdot])  # 取正值大小
    Ja = -np.array([P.Kpdot, P.Mqdot, P.Nrdot])
    M0 = np.array([P.m, P.m, P.m], dtype=float)
    J0 = np.array([P.Ixx, P.Iyy, P.Izz], dtype=float)
    # 简化：浸没越高附加越强
    k = float(np.clip(immersion_frac, 0.0, 1.0) * P.K_M)
    M_eff = M0 + k * Ma
    j = float(np.clip(immersion_frac, 0.0, 1.0) * P.K_J)
    J_eff = J0 + j * Ja
    return np.diag(M_eff), np.diag(J_eff)


def gravity_buoyancy_world(P: VehicleParams, immersion_frac: float) -> np.ndarray:
    """世界系重力+浮力（正向上）"""
    G = np.array([0.0, 0.0, -P.m * P.g], dtype=float)
    B = np.array([0.0, 0.0, buoyancy_force(P, immersion_frac)], dtype=float)
    return G + B


def body_dynamics_dot(state: HAUVState,
                      rpm4: np.ndarray,
                      D4: np.ndarray,
                      P: VehicleParams,
                      motors: MotorSet,
                      env: EnvState,
                      disturbance: Optional[Disturbance] = None) -> HAUVState:
    """6DoF动力学骨架（含OE-2022/Surfing-2023要素；风/波简化激励）
    - rpm4: 四桨转速 [rpm]
    - D4: 四桨浸没参数（-0.5全水下, 0全空气），内部映射到 0..1 的浸没率
    """
    x, y, z, phi, theta, psi = state.eta
    u, v, w, p, q, r = state.nu
    Rbn = euler_to_R(phi, theta, psi)          # 世界->机体
    Rnb = Rbn.T
    # 估算总体浸没比例（占位：四桨D4求均值映射）
    immersion_frac = float(np.clip(np.mean(0.5 - np.clip(D4, -0.5, 0.0)) / 0.5, 0.0, 1.0))

    # 有效质量/惯量
    M_trans, J_rot = mass_matrices(P, immersion_frac)

    # 推进：
    T4 = motors.thrusts(rpm4, D4)  # [N]
    B = quad_mixing_matrix(P.L, P.motor_tau_coeff)
    U = B @ T4  # [U1,U2,U3,U4]
    U1 = U[0]
    Mc = U[1:4]  # 控制力矩（机体系）

    # 推力方向：沿 -zb（机体下方）向上
    Fc_thruster_b = np.array([0.0, 0.0, U1], dtype=float)  # 直接作为机体系向上正

    # 二次阻尼
    Fd_b, Md_b = hydrodynamic_quadratic_damping(state.nu, P)

    # 环境扰动（风/波）：简化加成（机体系）
    F_env_b = np.zeros(3) if disturbance is None else disturbance.F_env_b
    M_env_b = np.zeros(3) if disturbance is None else disturbance.M_env_b

    # 重力+浮力（世界系）并转到机体
    GpBn = gravity_buoyancy_world(P, immersion_frac)  # n系
    GpBb = Rbn @ GpBn

    # 平动： (M_eff) * ν_dot_lin = F总
    F_total_b = Fc_thruster_b + Fd_b + F_env_b + GpBb
    nu_lin_dot = np.linalg.solve(M_trans, F_total_b)

    # 转动：J_eff * ω_dot = Mc + Md + M_env
    M_total_b = Mc + Md_b + M_env_b
    omega_dot = np.linalg.solve(J_rot, M_total_b)

    # 姿态与位置运动学
    T = euler_kinematics_matrix(phi, theta)
    euler_dot = np.linalg.solve(T, np.array([p, q, r], dtype=float))
    vel_world = Rnb @ np.array([u, v, w], dtype=float)

    d = HAUVState()
    d.eta = np.array([vel_world[0], vel_world[1], vel_world[2],
                      euler_dot[0], euler_dot[1], euler_dot[2]], dtype=float)
    d.nu = np.array([nu_lin_dot[0], nu_lin_dot[1], nu_lin_dot[2],
                     omega_dot[0], omega_dot[1], omega_dot[2]], dtype=float)
    return d


# =========================
# 5) 控制器骨架
# =========================

@dataclass
class SMCGains:
    # 位置/高度 SMC（OE-2022）
    lam_pos: float = 2.0
    mu_pos: float = 1.0
    lam_alt: float = 2.0
    mu_alt: float = 1.0
    # 姿态 SMC
    lam_att: float = 4.0
    mu_att: float = 1.5


class SMCController:
    """OE-2022风格的二阶SMC骨架（式(45)-(47)，(49)，(51)-(53)）"""
    def __init__(self, P: VehicleParams, gains: SMCGains):
        self.P = P
        self.G = gains

    def position_outer(self, pos: np.ndarray, vel: np.ndarray, pos_d: np.ndarray, vel_d: np.ndarray, acc_d: np.ndarray) -> Tuple[np.ndarray, float]:
        """输出期望姿态(φd, θd)与总推力U1（u_fd）
        简化：小角度近似与耦合弱化（参考OE-2022式(36),(49)思路）
        """
        e = pos_d - pos
        ed = vel_d - vel
        s = ed + self.G.lam_pos * e
        # 等效控制项（加速度指令）
        a_cmd = acc_d + self.G.lam_pos * ed - self.G.mu_pos * np.tanh(5.0 * s)
        # 将a_cmd映射为姿态与推力：简化法（期望φ,θ产生水平力，U1产生垂向力）
        # U1 ~ m(g - a_cmd_z) + 浮力项
        U1 = self.P.m * (self.P.g - a_cmd[2])  # 不含浮力，浮力在动力学中体现
        # 水平加速度要求 -> 倾角
        ax, ay = a_cmd[0], a_cmd[1]
        phi_d = np.clip(-ay / max(self.P.g, 1e-3), -np.deg2rad(25), np.deg2rad(25))
        theta_d = np.clip(ax / max(self.P.g, 1e-3), -np.deg2rad(25), np.deg2rad(25))
        return np.array([phi_d, theta_d]), float(U1)

    def altitude_channel(self, z: float, wz: float, z_d: float, zd_d: float, zdd_d: float) -> float:
        e = z_d - z
        ed = zd_d - wz
        s = ed + self.G.lam_alt * e
        a_z_cmd = zdd_d + self.G.lam_alt * ed - self.G.mu_alt * np.tanh(5.0 * s)
        U1 = self.P.m * (self.P.g - a_z_cmd)
        return float(U1)

    def attitude_inner(self, eul: np.ndarray, omega: np.ndarray, eul_d: np.ndarray, omega_d: np.ndarray, omegad_d: np.ndarray) -> np.ndarray:
        e = eul_d - eul
        ed = omega_d - omega  # 近似
        s = ed + self.G.lam_att * e
        tau_cmd = self.G.lam_att * ed - self.G.mu_att * np.tanh(5.0 * s)
        # 直接用比例转矩（简化）
        return tau_cmd


@dataclass
class DSCNDOParams:
    # 位置环
    K1: np.ndarray = field(default_factory=lambda: np.diag([2.0, 2.0, 2.0]))
    K2: np.ndarray = field(default_factory=lambda: np.diag([2.0, 2.0, 2.0]))
    K3: np.ndarray = field(default_factory=lambda: np.diag([1.5, 1.5, 1.5]))
    T1: np.ndarray = field(default_factory=lambda: np.diag([0.1, 0.1, 0.1]))
    T2: np.ndarray = field(default_factory=lambda: np.diag([0.1, 0.1, 0.1]))
    # 姿态环
    K4: np.ndarray = field(default_factory=lambda: np.diag([4.0, 4.0, 3.0]))  # φ,θ,ψ
    T3: np.ndarray = field(default_factory=lambda: np.diag([0.05, 0.05, 0.05]))
    # NDO增益（位置/姿态）
    lf: np.ndarray = field(default_factory=lambda: np.diag([10.0, 10.0, 10.0]))
    lt: np.ndarray = field(default_factory=lambda: np.diag([10.0, 10.0, 10.0]))


class DSCNDOController:
    """JOE-2022: 动态面控制 + 非线性扰动观测器（式(40)-(45),(42),(50),(53),(58),(59),(61)）骨架"""
    def __init__(self, P: VehicleParams, C: DSCNDOParams):
        self.P = P
        self.C = C
        # 过滤/观测器状态
        self.v1d_bar = np.zeros(3)
        self.v2d_bar = np.zeros(3)
        self.omega_d_bar = np.zeros(3)
        self.dhat_f = np.zeros(3)  # 位置扰动估计
        self.dhat_tau = np.zeros(3)  # 姿态扰动估计

    def step(self, dt: float,
             pos: np.ndarray, vel: np.ndarray, pos_d: np.ndarray, vel_d: np.ndarray, acc_d: np.ndarray,
             eul: np.ndarray, omega: np.ndarray, eul_d: np.ndarray, omegad_d: np.ndarray, omegadd_d: np.ndarray
             ) -> Tuple[float, np.ndarray]:
        # 位置环 Step1: 式(40)-(42)
        eps1 = pos - pos_d
        v1d = (-self.C.K1 @ eps1 + vel_d)
        self.v1d_bar += dt * (np.linalg.solve(self.C.T1, (v1d - self.v1d_bar)))

        # Step2: 次级误差与v2d
        eps2 = vel - self.v1d_bar
        v2d = -self.C.K2 @ eps2 + acc_d - self.dhat_f / max(self.P.m, 1e-3)
        self.v2d_bar += dt * (np.linalg.solve(self.C.T2, (v2d - self.v2d_bar)))

        # 控制总推力（U1）
        # 简化：沿机体z轴分配，抵消加速度需求
        U1 = self.P.m * (self.P.g - self.v2d_bar[2])

        # 扰动观测器更新（位置）式(59)骨架
        # dhat_f_dot = -lf * y1 (此处以 eps2 近似y1项) + ...
        self.dhat_f += dt * (-self.C.lf @ eps2)

        # 姿态环：目标姿态来自于横向加速度需求（简化），yaw来自eul_d[2]
        ax, ay = self.v2d_bar[0], self.v2d_bar[1]
        phi_d = np.clip(-ay / max(self.P.g, 1e-3), -np.deg2rad(25), np.deg2rad(25))
        theta_d = np.clip(ax / max(self.P.g, 1e-3), -np.deg2rad(25), np.deg2rad(25))
        psi_d = eul_d[2]
        eul_d_vec = np.array([phi_d, theta_d, psi_d])

        # 姿态误差与滤波（式(50),(53)）
        eps4 = eul - eul_d_vec
        omega_d = -self.C.K4 @ eps4 + omegad_d - self.dhat_tau  # 简化
        self.omega_d_bar += dt * (np.linalg.solve(self.C.T3, (omega_d - self.omega_d_bar)))

        # 力矩命令
        tau_cmd = self.P.inertia_matrix().diagonal() * (self.omega_d_bar - omega)

        # 姿态扰动观测器更新（式(61)骨架）
        self.dhat_tau += dt * (-self.C.lt @ (omega - self.omega_d_bar))

        return float(U1), tau_cmd


# =========================
# 6) 波/风扰动（简化Airy）
# =========================

@dataclass
class WaveParams:
    Hs: float = 0.2     # 有效波高 [m]
    T: float = 3.0      # 波周期 [s]
    beta: float = 0.0   # 波向（相对x轴）[rad]
    g: float = 9.81


def airy_wave(t: float, x: float, y: float, W: WaveParams) -> Tuple[float, float]:
    """简化Airy波：ζ= A cos(k·x - ωt)，a=surface加速度幅值"""
    A = W.Hs / 2.0
    omega = 2.0 * np.pi / W.T
    # 深水近似：k = ω^2/g
    k = omega ** 2 / W.g
    kx = k * (x * np.cos(W.beta) + y * np.sin(W.beta))
    zeta = A * np.cos(kx - omega * t)
    azeta = -A * omega ** 2 * np.cos(kx - omega * t)
    return zeta, azeta


# =========================
# 7) 任务/仿真器
# =========================

def rk4_step(f: Callable, y: HAUVState, h: float, *args, **kwargs) -> HAUVState:
    def add(a: HAUVState, b: HAUVState, scale: float) -> HAUVState:
        o = HAUVState()
        o.eta = a.eta + scale * b.eta
        o.nu = a.nu + scale * b.nu
        return o

    k1 = f(y, *args, **kwargs)
    k2 = f(add(y, k1, h/2.0), *args, **kwargs)
    k3 = f(add(y, k2, h/2.0), *args, **kwargs)
    k4 = f(add(y, k3, h), *args, **kwargs)

    yn = HAUVState()
    yn.eta = y.eta + h/6.0 * (k1.eta + 2*k2.eta + 2*k3.eta + k4.eta)
    yn.nu = y.nu + h/6.0 * (k1.nu + 2*k2.nu + 2*k3.nu + k4.nu)
    return yn


def water_exit_traj(t: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """OE-2022式(56)：z_d: -1 -> +1 m，40s内线性，x_d=y_d=0"""
    if t < 40.0:
        zd = -1.0 + 0.05 * t
        zdd = 0.05
    else:
        zd = 1.0
        zdd = 0.0
    xd, yd = 0.0, 0.0
    pos_d = np.array([xd, yd, zd])
    vel_d = np.array([0.0, 0.0, 0.05 if t < 40.0 else 0.0])
    acc_d = np.array([0.0, 0.0, 0.0])
    return pos_d, vel_d, acc_d


def demo_simulation(controller: str = "SMC",
                    total_time: float = 10.0,
                    dt: float = 0.01,
                    use_default_curves: bool = True,
                    air_csv: Optional[str] = None,
                    water_csv: Optional[str] = None) -> Dict[str, np.ndarray]:
    """最小可运行演示：水出场景，比较SMC/DSC骨架效果
    返回：轨迹/姿态/输入等时序
    """
    # 桨曲线
    if use_default_curves:
        air, water = default_prop_curves()
    else:
        assert air_csv and water_csv
        x_air, cols_air = load_curve_csv(air_csv)
        x_w, cols_w = load_curve_csv(water_csv)
        air = PropCurve("air", x_air, cols_air.get("thrust_N", None), power_W=cols_air.get("power_W", None))
        water = PropCurve("water", x_w, cols_w.get("thrust_N", None), power_W=cols_w.get("power_W", None))
    blend = InterfaceBlend(air, water)
    motors = MotorSet(blend, rpm_limits=(0.0, 6243.0))

    # 车辆与控制器
    P = VehicleParams()
    if controller.upper() == "SMC":
        ctrl = SMCController(P, SMCGains())
    else:
        ctrl = DSCNDOController(P, DSCNDOParams())

    # 初始状态
    st = HAUVState()
    # 初始：水下 -1m，静止
    st.eta[:3] = np.array([0.0, 0.0, -1.0])
    st.eta[3:] = np.zeros(3)
    st.nu[:] = 0.0

    # 数据缓存
    N = int(total_time / dt) + 1
    t_arr = np.linspace(0.0, total_time, N)
    x_arr = np.zeros((N, 3))
    eul_arr = np.zeros((N, 3))
    rpm_arr = np.zeros((N, 4))
    D_arr = np.zeros((N, 4))
    U_arr = np.zeros((N, 4))
    zeta_arr = np.zeros(N)

    # 简化：四桨D相同，线性从水到气（仅示例；应由波面/姿态/桨位置计算）
    def D_of_z(z_world: float) -> float:
        # z<=-0.5 全水下D=-0.5； z>=0 全空气D=0； 中间线性
        if z_world <= -0.5:
            return -0.5
        elif z_world >= 0.0:
            return 0.0
        else:
            return z_world * 1.0  # -0.5..0

    # 混控矩阵逆
    B = quad_mixing_matrix(P.L, P.motor_tau_coeff)
    B_inv = quad_mixing_inverse(B)

    # 控制内环：将(U1, τx,τy,τz)映射到四桨推力 -> 再映射rpm（用反向查表近似）
    def thrust_to_rpm(T: float, air_frac: float) -> float:
        # 简化：用默认曲线反解rpm。此处使用线性搜索近似（演示用）
        curve = blend.air_curve if air_frac >= 0.5 else blend.water_curve
        # 单桨推力在0..Tmax范围内反解
        rpm_grid = curve.rpm_grid
        T_grid = curve.thrust_N
        # 对应阈值
        idx = np.searchsorted(T_grid, T, side="left")
        if idx <= 0:
            return rpm_grid[0]
        if idx >= len(rpm_grid):
            return rpm_grid[-1]
        # 线性插值反解
        t0, t1 = T_grid[idx-1], T_grid[idx]
        r0, r1 = rpm_grid[idx-1], rpm_grid[idx]
        alpha = 0.0 if (t1 - t0) == 0 else (T - t0)/(t1 - t0)
        return float(r0 + alpha * (r1 - r0))

    # 主循环
    for k, t in enumerate(t_arr):
        pos = st.eta[:3]
        eul = st.eta[3:]
        vel = st.nu[:3]
        omega = st.nu[3:]

        pos_d, vel_d, acc_d = water_exit_traj(t)
        # 简化：yaw目标=0
        eul_d = np.array([0.0, 0.0, 0.0])
        omegad_d = np.zeros(3)
        omegadd_d = np.zeros(3)

        # 海况/波
        W = WaveParams()
        zeta, azeta = airy_wave(t, pos[0], pos[1], W)
        env = EnvState(wave_elev=zeta, wave_accel_world=np.array([0.0, 0.0, azeta]))
        zeta_arr[k] = zeta

        # 控制
        if isinstance(ctrl, SMCController):
            # 采用高度通道为主（与OE-2022水出任务一致）
            U1_alt = ctrl.altitude_channel(st.eta[2], st.nu[2], pos_d[2], vel_d[2], acc_d[2])
            att_d, _ = ctrl.position_outer(pos, vel, pos_d, vel_d, acc_d)
            phi_d, theta_d = att_d
            eul_cmd = np.array([phi_d, theta_d, 0.0])
            tau_cmd = ctrl.attitude_inner(eul, omega, eul_cmd, np.zeros(3), np.zeros(3))
            U_cmd = np.array([U1_alt, tau_cmd[0], tau_cmd[1], tau_cmd[2]])
        else:
            U1_cmd, tau_cmd = ctrl.step(dt, pos, vel, pos_d, vel_d, acc_d, eul, omega, eul_d, omegad_d, omegadd_d)
            U_cmd = np.array([U1_cmd, tau_cmd[0], tau_cmd[1], tau_cmd[2]])

        # 限幅（JOE-2022：单桨最大49.5N，rpm_max=6243）
        # 先分配四桨推力
        T4 = B_inv @ U_cmd
        T4 = np.clip(T4, 0.0, P.thrust_max_per_motor)

        # 由推力反解rpm（简化：按当前浸没状况与空气占比近似）
        D = np.array([D_of_z(st.eta[2])] * 4)
        air_frac = np.mean(blend.alpha_air(float(D[0])))
        rpm4 = np.array([thrust_to_rpm(T4[i], air_frac) for i in range(4)])
        rpm4 = np.clip(rpm4, 0.0, P.rpm_max)

        # 保存
        x_arr[k, :] = pos
        eul_arr[k, :] = eul
        rpm_arr[k, :] = rpm4
        D_arr[k, :] = D
        U_arr[k, :] = U_cmd

        # 积分一步
        st = rk4_step(lambda s: body_dynamics_dot(s, rpm4, D, P, motors, env, None), st, dt)

    return {
        "t": t_arr,
        "pos": x_arr,
        "eul": eul_arr,
        "rpm4": rpm_arr,
        "D4": D_arr,
        "U": U_arr,
        "zeta": zeta_arr
    }


# =========================
# 8) 简单自测
# =========================

def _quick_test():
    print("运行SMC水出演示 3s ...")
    out = demo_simulation(controller="SMC", total_time=3.0, dt=0.01, use_default_curves=True)
    print("pos(z) 末值:", out["pos"][-1, 2])
    print("rpm4 末值:", out["rpm4"][-1, :])
    print("Done.")


if __name__ == "__main__":
    _quick_test()
